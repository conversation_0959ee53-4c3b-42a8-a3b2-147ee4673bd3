# frozen_string_literal: true

module SubscriptionHelper
  # Stripe plan ID mappings
  PLAN_MAPPINGS = {
    'price_1R9Q55DYYVPVcCCrWQOwsKmT' => {
      name: 'Standard',
      description: 'Standard subscription plan',
      billing_cycle: 'Monthly',
      features: ['Basic features', 'Standard support']
    },
    'price_1R9Q66DYYVPVcCCrnqiXNafF' => {
      name: 'Premium',
      description: 'Premium subscription plan with advanced features',
      billing_cycle: 'Monthly',
      features: ['All Standard features', 'Premium support', 'Advanced analytics', 'Priority listing']
    }
  }.freeze

  # Map Stripe plan ID to user-friendly name
  def plan_display_name(plan_id)
    PLAN_MAPPINGS.dig(plan_id, :name) || 'Unknown Plan'
  end

  # Get plan description
  def plan_description(plan_id)
    PLAN_MAPPINGS.dig(plan_id, :description) || 'Subscription plan'
  end

  # Get billing cycle information
  def plan_billing_cycle(plan_id)
    PLAN_MAPPINGS.dig(plan_id, :billing_cycle) || 'Monthly'
  end

  # Get plan features
  def plan_features(plan_id)
    PLAN_MAPPINGS.dig(plan_id, :features) || []
  end

  # Check if plan is premium
  def premium_plan?(plan_id)
    plan_id == 'price_1R9Q66DYYVPVcCCrnqiXNafF'
  end

  # Check if plan is standard
  def standard_plan?(plan_id)
    plan_id == 'price_1R9Q55DYYVPVcCCrWQOwsKmT'
  end

  # Get subscription status badge classes
  def subscription_status_badge_classes(status)
    case status.to_s.downcase
    when "active"
      # Most prominent - darker stone background with white text for active status
      "inline-flex items-center rounded-md bg-stone-800 px-2 py-1 text-xs font-medium text-white ring-1 ring-inset ring-stone-700/20"
    when "trialing"
      # Secondary prominence - medium stone background for trial period
      "inline-flex items-center rounded-md bg-stone-200 px-2 py-1 text-xs font-medium text-stone-800 ring-1 ring-inset ring-stone-600/20"
    when "past_due"
      # Warning state - lighter stone with darker text for attention
      "inline-flex items-center rounded-md bg-stone-100 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-600/20"
    when "canceled", "cancelled"
      # Muted state - very light stone for cancelled subscriptions
      "inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-600 ring-1 ring-inset ring-stone-500/20"
    when "incomplete"
      # Attention needed - medium stone background
      "inline-flex items-center rounded-md bg-stone-100 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-600/20"
    when "incomplete_expired"
      # Expired state - lightest stone background
      "inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-600 ring-1 ring-inset ring-stone-500/20"
    when "unpaid"
      # Problem state - light stone with darker text for visibility
      "inline-flex items-center rounded-md bg-stone-100 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-600/20"
    else
      # Default fallback - neutral stone styling
      "inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-600 ring-1 ring-inset ring-stone-500/20"
    end
  end

  # Get subscription status display text
  def subscription_status_display(status)
    case status.to_s.downcase
    when "active"
      "Active"
    when "trialing"
      "Trial Period"
    when "past_due"
      "Past Due"
    when "canceled", "cancelled"
      "Cancelled"
    when "incomplete"
      "Incomplete"
    when "incomplete_expired"
      "Expired"
    when "unpaid"
      "Unpaid"
    else
      status.to_s.humanize
    end
  end

  # Format next billing date
  def format_billing_date(date)
    return 'Not available' unless date.present?
    
    if date.respond_to?(:strftime)
      date.strftime('%B %d, %Y')
    else
      date.to_s
    end
  end

  # Get subscription card classes for different plan types
  def subscription_card_classes(plan_id)
    base_classes = 'rounded-lg border p-6 shadow-sm'
    
    if premium_plan?(plan_id)
      "#{base_classes} border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50"
    elsif standard_plan?(plan_id)
      "#{base_classes} border-stone-200 bg-stone-50"
    else
      "#{base_classes} border-stone-200 bg-white"
    end
  end

  # Get plan badge classes
  def plan_badge_classes(plan_id)
    if premium_plan?(plan_id)
      'inline-flex items-center rounded-md bg-purple-50 px-2 py-1 text-xs font-medium text-purple-700 ring-1 ring-inset ring-purple-700/10'
    elsif standard_plan?(plan_id)
      'inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-700/10'
    else
      'inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-700/10'
    end
  end
end
