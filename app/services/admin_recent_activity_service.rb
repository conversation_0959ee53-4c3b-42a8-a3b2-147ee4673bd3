# frozen_string_literal: true

class AdminRecentActivityService
  CACHE_EXPIRY = 5.minutes

  def self.generate_recent_activity
    Rails
      .cache
      .fetch('admin_recent_activity', expires_in: CACHE_EXPIRY) do
        new.generate_recent_activity
      end
  end

  def self.clear_cache
    Rails.cache.delete('admin_recent_activity')
  end

  def generate_recent_activity
    {
      recent_users: recent_users,
      recent_jobs: recent_jobs,
      recent_applications: recent_applications,
      recent_chat_requests: recent_chat_requests,
    }
  end

  private

  def recent_users
    User.includes(:roles, :organizations).order(created_at: :desc).limit(5)
  end

  def recent_jobs
    Job.includes(:organization).order(created_at: :desc).limit(5)
  end

  def recent_applications
    JobApplication.includes(:job, :user).order(created_at: :desc).limit(5)
  end

  def recent_chat_requests
    ChatRequest.includes(:scout, :talent).order(created_at: :desc).limit(5)
  end
end
