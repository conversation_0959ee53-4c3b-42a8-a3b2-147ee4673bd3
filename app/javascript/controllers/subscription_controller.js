import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="subscription"
export default class extends Controller {
  static targets = ["loadingState", "content", "retryButton"]
  static values = { 
    loaded: <PERSON><PERSON><PERSON>,
    hasError: <PERSON><PERSON><PERSON> 
  }

  connect() {
    console.log("Subscription controller connected")
    
    // Show loading state initially if not loaded
    if (!this.loadedValue) {
      this.showLoading()
      // Simulate loading delay for better UX
      setTimeout(() => {
        this.showContent()
      }, 500)
    }
  }

  showLoading() {
    if (this.hasLoadingStateTarget) {
      this.loadingStateTarget.classList.remove("hidden")
    }
    if (this.hasContentTarget) {
      this.contentTarget.classList.add("hidden")
    }
  }

  showContent() {
    if (this.hasLoadingStateTarget) {
      this.loadingStateTarget.classList.add("hidden")
    }
    if (this.hasContentTarget) {
      this.contentTarget.classList.remove("hidden")
    }
    this.loadedValue = true
  }

  retry() {
    // Handle retry functionality for billing portal errors
    if (this.hasRetryButtonTarget) {
      this.retryButtonTarget.disabled = true
      this.retryButtonTarget.innerHTML = `
        <svg class="animate-spin -ml-1 mr-2 h-3 w-3 text-yellow-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Retrying...
      `
      
      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    }
  }

  // Handle subscription upgrade/downgrade button states
  handleSubscriptionAction(event) {
    const button = event.currentTarget
    const originalText = button.innerHTML
    
    // Disable button and show loading state
    button.disabled = true
    button.innerHTML = `
      <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      Processing...
    `

    // The form submission will handle the redirect
    // If there's an error, restore the button state
    setTimeout(() => {
      if (button.disabled) {
        button.disabled = false
        button.innerHTML = originalText
      }
    }, 10000) // 10 second timeout
  }

  // Handle billing portal link clicks
  handleBillingPortal(event) {
    const link = event.currentTarget
    const originalText = link.innerHTML
    
    // Show loading state
    link.innerHTML = `
      <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      Opening billing portal...
    `

    // Restore original text after a delay (in case the redirect doesn't happen)
    setTimeout(() => {
      link.innerHTML = originalText
    }, 3000)
  }
}
