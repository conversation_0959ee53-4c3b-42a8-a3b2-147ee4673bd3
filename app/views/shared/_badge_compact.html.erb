<%#
  Badge Component - Compact Version for Search Results

  Usage:
  render 'shared/badge_compact', badge_type: badge_type
  render 'shared/badge_compact', badge_type: badge_type, show_description: true
  render 'shared/badge_compact', badge_type: badge_type, class_name: 'ml-2'

  Parameters:
  - badge_type: BadgeType object (required)
  - show_description: boolean (default: true) - shows description on hover
  - class_name: additional CSS classes
  - data_attributes: hash of additional data attributes
  - icon_only: boolean (default: false) - shows only icon for very compact display
%>

<%
  # Set default values
  badge_type = local_assigns.fetch(:badge_type)
  show_description = local_assigns.fetch(:show_description, true)
  class_name = local_assigns.fetch(:class_name, '')
  data_attributes = local_assigns.fetch(:data_attributes, {})
  icon_only = local_assigns.fetch(:icon_only, false)
  
  # Validate badge_type
  unless badge_type.is_a?(BadgeType)
    raise ArgumentError, "badge_type must be a BadgeType object"
  end
  
  # Compact styling - smaller padding and text
  size_classes = if icon_only
    'px-1.5 py-1 text-xs'
  else
    'px-2 py-0.5 text-xs'
  end
  
  # Icon size for compact display
  icon_size_classes = if icon_only
    'text-xs'
  else
    'text-xs mr-1'
  end
  
  # Build CSS styles from badge_type
  badge_styles = [
    "background-color: #{badge_type.background_color}",
    "color: #{badge_type.text_color}",
    "border-color: #{badge_type.background_color}"
  ].join('; ')
  
  # Combine all CSS classes for compact badge with holographic effects
  badge_classes = [
    'badge-compact',
    'inline-flex',
    'items-center',
    'font-medium',
    'rounded-md',
    'border',
    'shadow-sm',
    'backdrop-filter',
    'backdrop-blur-sm',
    'bg-opacity-90',
    'transition-all',
    'duration-200',
    'ease-out',
    'transform-gpu',
    'will-change-transform',
    'cursor-pointer',
    'hover:shadow-md',
    'prismatic',
    size_classes,
    class_name
  ].compact.join(' ')
  
  # Prepare data attributes for Stimulus controllers with holographic values and modal trigger functionality
  final_data_attributes = {
    controller: 'badge badge-click',
    action: 'click->badge-click#click',
    badge_target: 'badge',
    badge_click_target: 'badge',
    badge_holographic_intensity_value: 0.3,
    badge_rotation_factor_value: 8,
    badge_glow_intensity_value: 0.6,
    badge_prismatic_effect_value: true,
    badge_scanline_effect_value: false,
    badge_refraction_pattern_value: 'none',
    badge_depth_effect_value: false,
    badge_glitch_effect_value: false,
    badge_shadow_color_value: 'rgba(0, 0, 0, 0.3)',
    badge_click_badge_id_value: badge_type.id,
    badge_click_badge_name_value: badge_type.name,
    badge_click_badge_description_value: badge_type.description,
    badge_click_badge_icon_value: badge_type.icon,
    badge_click_badge_background_color_value: badge_type.background_color,
    badge_click_badge_text_color_value: badge_type.text_color,
    badge_click_badge_criteria_value: "This badge is awarded for #{badge_type.name.downcase} and represents exceptional achievement on the platform."
  }.merge(data_attributes)
  
  # Tooltip content for description
  tooltip_content = show_description ? badge_type.description : nil
%>

<div
  class="<%= badge_classes %>"
  style="<%= badge_styles %>"
  <% final_data_attributes.each do |key, value| %>
    data-<%= key.to_s.dasherize %>="<%= value %>"
  <% end %>
  role="img"
  aria-label="<%= html_escape("#{badge_type.name} badge") %>"
>
  <%# Badge Icon %>
  <% if badge_type.icon.present? %>
    <% begin %>
      <%= phosphor_icon badge_type.icon, style: "bold", class: "badge-icon #{icon_size_classes}", "aria-hidden": "true" %>
    <% rescue PhosphorIcons::IconNotFoundError %>
      <%= phosphor_icon "placeholder", style: "bold", class: "badge-icon #{icon_size_classes}", "aria-hidden": "true" %>
    <% end %>
  <% end %>

  <%# Badge Name (hidden in icon-only mode) %>
  <% unless icon_only %>
    <span class="font-medium badge-name">
      <%= badge_type.name %>
    </span>
  <% end %>
</div>

<%# Enhanced CSS for compact badge effects %>
<style>
  .badge-compact {
    position: relative;
    overflow: hidden;
    max-width: 120px; /* Prevent badges from getting too wide in search results */
  }
  
  .badge-compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
    border-radius: inherit;
  }
  

  
  /* Compact badge specific hover effects */
  .badge-compact:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  /* Ensure text doesn't overflow in compact mode */
  .badge-compact .badge-name {
    max-width: 80px;
  }
  
  /* Icon-only mode adjustments */
  .badge-compact[data-icon-only="true"] {
    max-width: 24px;
    justify-content: center;
  }
  
  /* Ensure smooth transitions for compact badge elements */
  .badge-compact .badge-icon {
    transition: transform 0.2s ease-out;
  }
  
  /* Accessibility: Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .badge-compact {
      transition: none !important;
    }
    
    .badge-compact .badge-icon {
      transition: none !important;
    }
    
    .badge-compact:hover {
      transform: none !important;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .badge-compact {
      border-width: 2px;
      backdrop-filter: none;
      background-opacity: 1 !important;
    }
  }
  
  /* Mobile responsiveness */
  @media (max-width: 640px) {
    .badge-compact {
      max-width: 100px;
    }
    
    .badge-compact .badge-name {
      max-width: 60px;
    }
  }
  
  /* Print styles */
  @media print {
    .badge-compact {
      background: white !important;
      color: black !important;
      border: 1px solid black !important;
      box-shadow: none !important;
      transform: none !important;
    }
    

  }
</style>
