<nav class="bg-black shadow" data-controller="reveal">
  <div class="px-2 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <div class="relative flex justify-between h-16">
      <div class="absolute inset-y-0 left-0 flex items-center sm:hidden">
        <!-- Mobile menu button -->
        <button data-action="reveal#toggle" type="button" class="relative inline-flex items-center justify-center p-2 rounded-md text-stone-400 hover:bg-black hover:text-stone-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-stone-500" aria-controls="mobile-menu" aria-expanded="false">
          <span class="absolute -inset-0.5"></span>
          <span class="sr-only">Open main menu</span>
          <svg class="block w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
          <svg class="hidden w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div class="flex items-center justify-center flex-1 pb-1 sm:items-stretch sm:justify-start">
        <%= link_to super_admin_root_path, class: "flex mr-4 flex-shrink-0 items-center" do %>
          <span class="items-center w-auto h-8 text-2xl font-medium text-white letter-spacing-title">ghostwrote</span>
          <span class="items-center w-auto h-8 text-2xl font-medium text-stone-300 letter-spacing-title">.</span>
          <span class="px-2 py-1 ml-2 text-xs font-semibold rounded bg-stone-800 text-stone-100">SUPER ADMIN</span>
        <% end %>
        
        <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
          <%= link_to "Dashboard", super_admin_root_path, class: "inline-flex items-center border-b-2 #{current_page?(super_admin_root_path) ? 'border-stone-300' : 'border-transparent'} px-1 pt-1 text-sm font-normal text-white hover:text-stone-300" %>
          <%= link_to "User Masquerade", super_admin_user_masquerade_index_path, class: "inline-flex items-center border-b-2 #{current_page?(super_admin_user_masquerade_index_path) ? 'border-stone-300' : 'border-transparent'} px-1 pt-1 text-sm font-normal text-white hover:text-stone-300" %>
          <%= link_to "Badge Management", super_admin_badge_types_path, class: "inline-flex items-center border-b-2 #{request.path.start_with?('/super_admin/badge') ? 'border-stone-300' : 'border-transparent'} px-1 pt-1 text-sm font-normal text-white hover:text-stone-300" %>
          <%= link_to "Admin Interface", super_admin_admin_dashboard_path, class: "inline-flex items-center border-b-2 #{request.path.start_with?('/super_admin/admin') ? 'border-stone-300' : 'border-transparent'} px-1 pt-1 text-sm font-normal text-white hover:text-stone-300" %>
        </div>
      </div>
      
      <div class="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">
        <% if Current.user %>
          <div data-controller="dropdown" class="relative flex items-center">
            <div class="relative">
              <button type="button" data-dropdown-target="button" class="relative flex items-center justify-center w-8 h-8 text-sm bg-white rounded-full focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" id="user-menu-button" aria-expanded="false" aria-haspopup="true" data-action="dropdown#toggle">
                <span class="absolute -inset-1.5"></span>
                <span class="sr-only">Open user menu</span>
                <% if Current.user.avatar.attached? && Current.user.avatar.representable? %>
                  <%= image_tag url_for(Current.user.avatar), class: "w-8 h-8 rounded-full object-cover", alt: (Current.user.name.presence || "User Avatar") %>
                <% else %>
                  <% user_initials = Current.user.initials %>
                  <% display_initials = user_initials.present? && user_initials.strip.present? ? user_initials.strip.first(2).upcase : "XX" %>
                  <div class="flex items-center justify-center w-8 h-8 text-white rounded-full bg-stone-600" title="<%= Current.user.name.presence || "User" %>">
                    <span class="text-sm font-semibold"><%= display_initials %></span>
                  </div>
                <% end %>
              </button>

              <div
                data-dropdown-target="menu"
                data-transition-enter-from="opacity-0 scale-95"
                data-transition-enter-to="opacity-100 scale-100"
                data-transition-leave-from="opacity-100 scale-100"
                data-transition-leave-to="opacity-0 scale-95"
                class="absolute right-0 z-10 hidden w-48 py-1 mt-2 transition duration-200 ease-out origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
                <%= link_to "Back to Main App", root_path, class: "block px-4 py-2 text-sm text-stone-700", role: "menuitem", tabindex: "-1" %>
                <%= button_to "Sign out", global_sign_out_path, method: :delete, class: "block w-full text-left px-4 py-2 text-sm text-stone-700", role: "menuitem", tabindex: "-2" %>
              </div>
            </div>
            
            <div class="items-center hidden ml-3 sm:flex">
              <div class="flex flex-col">
                <span class="text-sm font-medium text-white"><%= Current.user.name %></span>
                <span class="text-sm rounded-md text-stone-300"><%= Current.user.admin_role_name || 'Administrator' %></span>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Mobile menu -->
  <div data-reveal-target="item" class="hidden" id="mobile-menu">
    <div class="pt-2 pb-4 space-y-1">
      <%= link_to "Dashboard", super_admin_root_path, class: "block py-2 pl-3 pr-4 text-base font-medium text-white border-l-4 #{current_page?(super_admin_root_path) ? 'border-stone-300 bg-stone-800' : 'border-transparent hover:border-stone-300 hover:bg-stone-800'}" %>
      <%= link_to "User Masquerade", super_admin_user_masquerade_index_path, class: "block py-2 pl-3 pr-4 text-base font-medium text-white border-l-4 #{current_page?(super_admin_user_masquerade_index_path) ? 'border-stone-300 bg-stone-800' : 'border-transparent hover:border-stone-300 hover:bg-stone-800'}" %>
      <%= link_to "Badge Management", super_admin_badge_types_path, class: "block py-2 pl-3 pr-4 text-base font-medium text-white border-l-4 #{request.path.start_with?('/super_admin/badge') ? 'border-stone-300 bg-stone-800' : 'border-transparent hover:border-stone-300 hover:bg-stone-800'}" %>
      <%= link_to "Admin Interface", super_admin_admin_dashboard_path, class: "block py-2 pl-3 pr-4 text-base font-medium text-white border-l-4 #{request.path.start_with?('/super_admin/admin') ? 'border-stone-300 bg-stone-800' : 'border-transparent hover:border-stone-300 hover:bg-stone-800'}" %>
    </div>
  </div>
</nav>
