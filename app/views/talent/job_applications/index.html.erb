<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full px-16 py-8 mx-auto max-w-7xl">
    <% if @job_applications.any? %>

    <div class="mb-2 text-2xl font-semibold text-stone-900">
    Applications
    </div>
    <div class="pb-2 mb-4 text-sm text-stone-600 ">
Here is a list of applications sent by you.
    </div >
      
    <!-- Segmented Control for Application Status -->
    <div class="pb-4 mb-6 border-stone-200 text-stone-600 ">
      <div class="inline-flex rounded-md shadow-sm" role="group">
        <% if @all_applications_count.to_i > 0 %>
          <%= link_to talent_job_applications_path, class: "px-4 py-2 text-sm font-medium #{params[:status].blank? ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border border-stone-200 rounded-l-lg focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            All <span class="ml-1 text-xs opacity-70">(<%= @all_applications_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border rounded-l-lg cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            All <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @applied_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "applied"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'applied' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Applied <span class="ml-1 text-xs opacity-70">(<%= @applied_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Applied <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @reviewed_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "reviewed"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'reviewed' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Reviewed <span class="ml-1 text-xs opacity-70">(<%= @reviewed_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Reviewed <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @qualified_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "qualified"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'qualified' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Qualified <span class="ml-1 text-xs opacity-70">(<%= @qualified_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Qualified <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @offered_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "offered"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'offered' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Offered <span class="ml-1 text-xs opacity-70">(<%= @offered_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Offered <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @accepted_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "accepted"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'accepted' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Accepted <span class="ml-1 text-xs opacity-70">(<%= @accepted_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Accepted <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
        
        <% if @withdrawn_count.to_i > 0 %>
          <%= link_to talent_job_applications_path(status: "withdrawn"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'withdrawn' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 rounded-r-lg focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Withdrawn <span class="ml-1 text-xs opacity-70">(<%= @withdrawn_count || 0 %>)</span>
          <% end %>
        <% else %>
          <span class="px-4 py-2 text-sm font-medium border-t border-b border-r rounded-r-lg cursor-not-allowed text-stone-400 bg-stone-100 border-stone-200">
            Withdrawn <span class="ml-1 text-xs opacity-70">(0)</span>
          </span>
        <% end %>
      </div>
    </div>
      <div class="border rounded-md border-stone-200">
        <% @job_applications.each do |application| %>
          <div class="p-6 border-b last:border-none border-stone-200">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <!-- Job Title and Status Header -->
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <%= link_to talent_job_path(application.job), class: "mr-4 text-lg font-medium text-stone-900 hover:underline" do %>
                      <%= application.job.title %> 
                    <% end %>
                    <p class="<%= status_color_class(application.status)%> text-xs font-medium px-2 py-0.5 rounded-md">
                      <%= application.status.capitalize %>
                    </p>
                  </div>
                  <!-- Add application date -->
                  <p class="text-xs text-stone-500">
                    Applied <%= time_ago_in_words(application.applied_at || application.created_at) %> ago
                  </p>
                </div>
                
                <!-- Company name -->
                <p class="mt-1 text-sm text-stone-600">
                  <%= application.job.organization.name %>
                </p>
                
                <!-- Application Progress Timeline -->
                <div class="mt-4 mb-4">
                  <div class="relative">
                    <div class="absolute inset-0 flex items-center" aria-hidden="true">
                      <div class="w-full border-t border-stone-200"></div>
                    </div>
                    <div class="relative flex justify-between">
                      <% statuses = ["applied", "reviewed", "qualified", "offered", "accepted"] %>
                      <% current_index = statuses.index(application.status) || 0 %>
                      
                      <% statuses.each_with_index do |status, index| %>
                        <% is_active = index <= current_index %>
                        <% is_current = index == current_index %>
                        
                        <div class="flex flex-col items-center">
                          <span class="relative flex w-3 h-3">
                            <span class="<%= is_active ? 'bg-stone-900' : 'bg-stone-200' %> h-3 w-3 rounded-full"></span>
                            <% if is_current %>
                              <span class="absolute inline-flex w-full h-full rounded-full opacity-75 animate-ping bg-stone-400"></span>
                            <% end %>
                          </span>
                          <span class="mt-1 text-xs <%= is_active ? 'font-medium text-stone-900' : 'text-stone-400' %>">
                            <%= status.capitalize %>
                          </span>
                        </div>
                      <% end %>
                    </div>
                  </div>
                </div>
                
                <!-- Next Steps Information -->
                <div class="mt-3 mb-4 text-sm">
                  <% case application.status %>
                  <% when "applied" %>
                    <div class="p-3 border rounded-md bg-stone-50 border-stone-200">
                      <p class="text-stone-700"><span class="font-medium">Next:</span> Your application is being reviewed by the employer.</p>
                    </div>
                  <% when "reviewed" %>
                    <div class="p-3 border rounded-md bg-stone-50 border-stone-200">
                      <p class="text-stone-700"><span class="font-medium">Next:</span> Employer is evaluating your qualifications.</p>
                    </div>
                  <% when "qualified" %>
                    <div class="p-3 border rounded-md bg-stone-50 border-stone-200">
                      <p class="text-stone-700"><span class="font-medium">Next:</span> You may receive an offer if selected as the final candidate.</p>
                    </div>
                  <% when "offered" %>
                    <div class="p-3 border border-blue-200 rounded-md bg-blue-50">
                      <p class="text-blue-700"><span class="font-medium">Action required:</span> Review and respond to your job offer.</p>
                    </div>
                  <% when "accepted" %>
                    <div class="p-3 border border-green-200 rounded-md bg-green-50">
                      <p class="text-green-700"><span class="font-medium">Congratulations!</span> You've accepted this position.</p>
                    </div>
                  <% when "withdrawn" %>
                    <div class="p-3 border rounded-md bg-stone-50 border-stone-200">
                      <p class="text-stone-700">You've withdrawn this application.</p>
                    </div>
                  <% end %>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between gap-3 mt-4">
                  <div>
                    <!-- Last activity timestamp -->
                    <% if application.updated_at.present? %>
                      <p class="text-xs text-stone-500">
                        Status updated: <%= time_ago_in_words(application.updated_at) %> ago
                      </p>
                    <% end %>
                  </div>

                  <div class="flex">
                    <%= link_to talent_job_path(application.job), class: "mr-2 inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-stone-700 bg-white hover:text-stone-900 underline focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
                      View Job 
                    <% end %>
                    <%= link_to talent_job_application_path(application), method: :post, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-900 hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
                      Edit my Application 
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="flex flex-col items-center justify-center h-[70vh]">
        <%= phosphor_icon "tray", style: "duotone", class: "w-16 h-16"%>
        <h3 class="mt-4 text-lg font-medium text-stone-900">No applications</h3>
        <p class="mt-1 text-sm text-stone-500">
          You don't have any job applications at the moment.
        </p>
        <div class="mt-8">
          <%= link_to talent_jobs_path, class: "inline-flex items-center px-6 py-3 text-base font-medium text-white bg-stone-900 border border-transparent rounded-md shadow-sm hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
            Source Jobs
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 ml-2 -mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
