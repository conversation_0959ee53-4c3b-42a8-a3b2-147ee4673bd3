<!-- Job Listing Card -->
<div class="mx-auto mb-6 overflow-hidden bg-white border rounded-lg shadow-lg border-stone-200">
  <div class="p-6">
    <!-- Header Section -->
    <div class="pb-4 mb-5 border-b border-stone-200">
      <div class="flex items-start justify-between">
        <div>
          <div class="flex items-center gap-2 mb-1">
            <!-- Job Status Badge -->
            <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-<%= job.active? ? 'green-100 text-green-700' : 'red-100 text-red-700' %>">
              <%= job.active? ? "Active" : "Expired" %>
            </span>
            
            <!-- Company Name -->
            <span class="text-sm text-stone-500"><%= job.organization&.name || "Organization" %></span>
            
            <% if false %> <!-- Placeholder for premium badge logic if needed in the future -->
            <!-- Premium Badge (if needed) -->
            <span class="bg-gradient-to-r from-amber-500 to-yellow-400 text-white px-2 py-0.5 text-xs font-bold rounded-md flex items-center">
              <!-- SVG for Award icon -->
              <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3 mr-1" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="8" r="6"></circle>
                <path d="M15.477 12.89 17 22l-5-3-5 3 1.523-9.11"></path>
              </svg>
              PREMIUM
            </span>
            <% end %>
          </div>
          
          <!-- Job Title -->
          <h3 class="text-xl font-bold text-stone-900"><%= job.title %></h3>
        </div>
        
        <div class="flex items-center gap-2">
          <!-- Posted Time -->
          <div class="flex items-center gap-1 text-sm text-stone-500">
            <!-- SVG for Clock icon -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            <span>Posted <%= time_ago_in_words(job.created_at) %> ago</span>
          </div>
          
          <!-- Action Buttons -->
          <div class="flex gap-2">
            <%# Removed eye button link_to %>
            
            <!-- Save Button -->
            <%= render "talent/jobs/save_button", job: job %>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Application Deadline -->
    <div class="mb-6 bg-<%= job.active? ? 'blue-50 border border-blue-100' : 'red-50 border border-red-100' %> rounded-md p-4">
      <div class="flex items-center">
        <!-- SVG for Calendar icon -->
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-<%= job.active? ? 'blue-500' : 'red-500' %>" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
          <line x1="16" y1="2" x2="16" y2="6"></line>
          <line x1="8" y1="2" x2="8" y2="6"></line>
          <line x1="3" y1="10" x2="21" y2="10"></line>
        </svg>
        
        <div>
          <div class="text-sm font-semibold text-<%= job.active? ? 'blue-800' : 'red-800' %>">APPLICATION DEADLINE</div>
          <div class="text-lg font-medium text-stone-900">
            <%= job.application_deadline&.strftime("%b %-d, %Y at %I:%M %p") || "No deadline specified" %>
          </div>
        </div>
        
        <div class="ml-auto">
          <span class="px-3 py-1 text-sm font-medium rounded-md bg-<%= job.active? ? 'blue-200 text-blue-800' : 'red-200 text-red-800' %>">
            <%= pluralize(job.job_applications_count, 'application') %>
          </span>
        </div>
      </div>
    </div>
    
    <!-- Info Cards Grid -->
    <div class="grid grid-cols-3 gap-4 mb-6">
      <!-- Category -->
      <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
        <div class="mb-1 text-xs text-stone-500">Category</div>
        <div class="flex items-center text-sm font-medium text-stone-800">
          <!-- SVG for FileText icon -->
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-blue-600" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <line x1="10" y1="9" x2="8" y2="9"></line>
          </svg>
          <span><%= job.job_category&.titleize || "Not specified" %></span>
        </div>
      </div>
      
      <!-- Platform -->
      <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
        <div class="mb-1 text-xs text-stone-500">Platform</div>
        <div class="flex items-center text-sm font-medium text-stone-800">
          <!-- SVG for Twitter icon -->
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-blue-500" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
          </svg>
          <span><%= job.platform&.titleize || "Not specified" %></span>
        </div>
      </div>
      
      <!-- Work Duration -->
      <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
        <div class="mb-1 text-xs text-stone-500">Duration</div>
        <div class="flex items-center text-sm font-medium text-stone-800">
          <!-- SVG for Clock icon -->
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-amber-500" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
          <span><%= job.work_duration&.titleize || "Not specified" %></span>
        </div>
      </div>
      
      <!-- Budget -->
      <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
        <div class="mb-1 text-xs text-stone-500">Budget</div>
        <div class="flex items-center text-sm font-medium text-stone-800">
          <!-- SVG for DollarSign icon -->
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-green-600" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="12" y1="1" x2="12" y2="23"></line>
            <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
          </svg>
          <span><%= job.budget_display %></span>
        </div>
      </div>
      
      <!-- Involvement Level -->
      <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
        <div class="mb-1 text-xs text-stone-500">Involvement</div>
        <div class="flex items-center text-sm font-medium text-stone-800">
          <!-- SVG for Users icon -->
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-purple-600" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          <span><%= job.involvement_level&.titleize || "Not specified" %></span>
        </div>
      </div>
      
      <!-- Outcome -->
      <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
        <div class="mb-1 text-xs text-stone-500">Outcome</div>
        <div class="flex items-center text-sm font-medium text-stone-800">
          <!-- SVG for Target icon -->
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-green-600" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <circle cx="12" cy="12" r="6"></circle>
            <circle cx="12" cy="12" r="2"></circle>
          </svg>
          <span><%= job.outcome&.humanize || "Drive traffic to offers" %></span>
        </div>
      </div>
    </div>
    
    <!-- Job Details Section -->
    <div class="mb-6">
      <h4 class="mb-3 text-sm font-semibold uppercase text-stone-700">JOB DETAILS</h4>
      <div class="mb-4">
        <h5 class="mb-1 text-sm text-stone-600">Description</h5>
        <p class="text-stone-800"><%= job.description&.truncate(200) || "No description provided" %></p>
      </div>
      
      <!-- Target Audience -->
      <% if job.target_audience_description.present? %>
        <div class="mb-4">
          <h5 class="mb-1 text-sm text-stone-600">Target Audience</h5>
          <p class="text-stone-800"><%= job.target_audience_description.truncate(150) %></p>
        </div>
      <% end %>
      
      <!-- Requirements -->
      <% if job.requirements.present? %>
        <div class="mb-4">
          <h5 class="mb-1 text-sm text-stone-600">Requirements</h5>
          <p class="text-stone-800"><%= job.requirements.truncate(150) %></p>
        </div>
      <% end %>
      
      <!-- Topics -->
      <div class="mb-4">
        <h5 class="mb-1 text-sm text-stone-600">Topics</h5>
        <div class="flex flex-wrap gap-2">
          <% (job.topics || ['E-Commerce & Dropshipping', 'SEO', 'Web 3']).each do |topic| %>
            <span class="px-3 py-1 text-xs rounded-full text-stone-700 bg-stone-100">
              <%= topic %>
            </span>
          <% end %>
        </div>
      </div>
      
      <!-- Category-Specific Details -->
      <% if job.newsletter? %>
        <div class="mb-4">
          <h5 class="mb-1 text-sm text-stone-600">Newsletter Details</h5>
          <div class="flex gap-4 text-sm text-stone-700">
            <span class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-blue-500" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <%= job.newsletter_frequency&.humanize || "Weekly" %>
            </span>
            <span class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-green-500" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
              </svg>
              <%= job.newsletter_length&.humanize || "300-600 words" %>
            </span>
          </div>
        </div>
      <% elsif job.lead_magnet? %>
        <div class="mb-4">
          <h5 class="mb-1 text-sm text-stone-600">Lead Magnet Type</h5>
          <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-700">
            <%= job.lead_magnet_type&.humanize || "Ebook" %>
          </span>
        </div>
      <% end %>
    </div>
    
    <!-- Additional Information Section -->
    <% if job.useful_info.present? %>
      <div class="mb-6">
        <h4 class="mb-3 text-sm font-semibold uppercase text-stone-700">ADDITIONAL INFORMATION</h4>
        <div class="p-4 border rounded-lg border-stone-200 bg-stone-50">
          <div class="flex items-start gap-2">
            <!-- SVG for Info icon -->
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-stone-500 mt-0.5 flex-shrink-0" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="16" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
            <div>
              <p class="text-sm text-stone-600"><%= job.useful_info %></p>
            </div>
          </div>
        </div>
      </div>
    <% end %>
    
    <!-- Footer with action button -->
    <div class="flex justify-end">
      <% if job.active? %>
        <% if Current.user.job_applications.exists?(job: job) %>
          <span class="px-6 py-2.5 rounded-md text-sm font-medium bg-green-100 text-green-800">
            Application Submitted
            <svg xmlns="http://www.w3.org/2000/svg" class="inline-block w-4 h-4 ml-2" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
          </span>
        <% else %>
          <%= link_to new_talent_job_job_application_path(job), class: "px-6 py-2.5 rounded-md text-sm font-medium bg-indigo-600 text-white hover:bg-indigo-700", data: { turbo: false } do %>
            Apply Now
            <svg xmlns="http://www.w3.org/2000/svg" class="inline-block w-4 h-4 ml-2" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="5" y1="12" x2="19" y2="12"></line>
              <polyline points="12 5 19 12 12 19"></polyline>
            </svg>
          <% end %>
        <% end %>
      <% else %>
        <button class="px-6 py-2.5 rounded-md text-sm font-medium bg-stone-200 text-stone-500 cursor-not-allowed">
          Applications Closed
          <svg xmlns="http://www.w3.org/2000/svg" class="inline-block w-4 h-4 ml-2" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="5" y1="12" x2="19" y2="12"></line>
            <polyline points="12 5 19 12 12 19"></polyline>
          </svg>
        </button>
      <% end %>
    </div>
  </div>
</div>
