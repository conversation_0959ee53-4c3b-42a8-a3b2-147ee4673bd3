<% content_for :page_title, "Badge Analytics Dashboard" %>
<% content_for :page_description, "Comprehensive analytics for badge performance and user engagement" %>

<div class="mx-auto max-w-7xl">
  <!-- Page Header -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-stone-900">Badge Analytics Dashboard</h1>
          <p class="mt-1 text-sm text-stone-500">Track badge performance, user engagement, and analytics insights</p>
        </div>
        <div class="flex items-center space-x-3">
          <%= link_to distribution_super_admin_badge_analytics_path(date_range: params[:date_range], badge_type_id: params[:badge_type_id]),
                      class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Distribution Report
          <% end %>
          <%= link_to performance_super_admin_badge_analytics_path,
                      class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            Performance Monitor
          <% end %>
          <%= link_to export_super_admin_badge_analytics_path(format: :csv, date_range: params[:date_range], badge_type_id: params[:badge_type_id]),
                      class: "inline-flex items-center px-4 py-2 border border-stone-300 rounded-md shadow-sm text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export CSV
          <% end %>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="px-6 py-4 bg-stone-50">
      <%= form_with url: super_admin_badge_analytics_path, method: :get, local: true, class: "flex items-center space-x-4" do |form| %>
        <div class="flex items-center space-x-2">
          <label for="date_range" class="text-sm font-medium text-stone-700">Date Range:</label>
          <%= form.select :date_range, 
                          options_for_select([
                            ['Last 7 days', '7'],
                            ['Last 14 days', '14'],
                            ['Last 30 days', '30'],
                            ['Last 90 days', '90'],
                            ['Last year', '365']
                          ], params[:date_range] || '30'),
                          {},
                          { class: "rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm" } %>
        </div>

        <div class="flex items-center space-x-2">
          <label for="badge_type_id" class="text-sm font-medium text-stone-700">Badge Type:</label>
          <%= form.select :badge_type_id, 
                          options_from_collection_for_select(@badge_types, :id, :name, params[:badge_type_id]),
                          { prompt: 'All Badge Types' },
                          { class: "rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm" } %>
        </div>

        <%= form.submit "Apply Filters", class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        
        <% if params[:date_range].present? || params[:badge_type_id].present? %>
          <%= link_to "Clear Filters", super_admin_badge_analytics_path, class: "text-sm text-stone-500 hover:text-stone-700" %>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Overview Metrics -->
  <div class="overview-metrics mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Overview Metrics</h2>
      <p class="text-sm text-stone-500">
        Showing data for the last <%= @date_range.to_i %> days
        <% if @selected_badge_type %>
          for "<%= @selected_badge_type.name %>" badges
        <% end %>
      </p>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-blue-600"><%= @analytics[:overview][:total_badges_assigned] %></div>
          <div class="text-sm text-stone-600">Badges Assigned</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-green-600"><%= @analytics[:overview][:total_badge_clicks] %></div>
          <div class="text-sm text-stone-600">Total Clicks</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-purple-600"><%= @analytics[:overview][:total_badge_views] %></div>
          <div class="text-sm text-stone-600">Total Views</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-orange-600"><%= @analytics[:overview][:unique_badge_clickers] %></div>
          <div class="text-sm text-stone-600">Unique Clickers</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-teal-600"><%= @analytics[:overview][:unique_badge_viewers] %></div>
          <div class="text-sm text-stone-600">Unique Viewers</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-indigo-600"><%= @analytics[:overview][:users_with_badges] %></div>
          <div class="text-sm text-stone-600">Users with Badges</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-amber-600"><%= @analytics[:overview][:avg_clicks_per_badge] %></div>
          <div class="text-sm text-stone-600">Avg Clicks/Badge</div>
        </div>
        <div class="p-4 border border-stone-200 rounded-lg">
          <div class="text-2xl font-bold text-rose-600"><%= @analytics[:overview][:avg_views_per_badge] %></div>
          <div class="text-sm text-stone-600">Avg Views/Badge</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Trends and Performance Grid -->
  <div class="grid grid-cols-1 gap-8 mb-8 lg:grid-cols-2">
    <!-- Trends -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Trends</h2>
        <p class="text-sm text-stone-500">Comparison with previous period</p>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-stone-600">Badge Clicks</span>
            <div class="flex items-center space-x-2">
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:trends][:clicks][:current_period] %></span>
              <span class="text-sm <%= @analytics[:trends][:clicks][:change_percent] >= 0 ? 'text-green-600' : 'text-red-600' %>">
                <%= @analytics[:trends][:clicks][:change_percent] >= 0 ? '+' : '' %><%= @analytics[:trends][:clicks][:change_percent] %>%
              </span>
            </div>
          </div>
          <div class="text-xs text-stone-500">vs <%= @analytics[:trends][:clicks][:previous_period] %> in previous period</div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-stone-600">Badge Views</span>
            <div class="flex items-center space-x-2">
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:trends][:views][:current_period] %></span>
              <span class="text-sm <%= @analytics[:trends][:views][:change_percent] >= 0 ? 'text-green-600' : 'text-red-600' %>">
                <%= @analytics[:trends][:views][:change_percent] >= 0 ? '+' : '' %><%= @analytics[:trends][:views][:change_percent] %>%
              </span>
            </div>
          </div>
          <div class="text-xs text-stone-500">vs <%= @analytics[:trends][:views][:previous_period] %> in previous period</div>
        </div>
      </div>
    </div>

    <!-- User Engagement -->
    <div class="user-engagement bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">User Engagement</h2>
        <p class="text-sm text-stone-500">Badge interaction rates</p>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-stone-600">Click Engagement Rate</span>
            <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:click_engagement_rate] %>%</span>
          </div>
          <div class="text-xs text-stone-500"><%= @analytics[:user_engagement][:users_with_badge_clicks] %> of <%= @analytics[:user_engagement][:total_users_with_badges] %> users clicked badges</div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-stone-600">View Engagement Rate</span>
            <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:view_engagement_rate] %>%</span>
          </div>
          <div class="text-xs text-stone-500"><%= @analytics[:user_engagement][:users_with_badge_views] %> of <%= @analytics[:user_engagement][:total_users_with_badges] %> users had badge views</div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-stone-600">Avg Badges per User</span>
            <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:avg_badges_per_user] %></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Badge Performance Table -->
  <div class="badge-performance mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Badge Performance</h2>
      <p class="text-sm text-stone-500">Individual badge type analytics</p>
    </div>
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-stone-200">
        <thead class="bg-stone-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Badge</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Assignments</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Clicks</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Views</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">CTR</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Engagement</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-stone-200">
          <% @analytics[:badge_performance].each do |badge_data| %>
            <% badge = badge_data[:badge_type] %>
            <% metrics = badge_data[:metrics] %>
            <tr class="hover:bg-stone-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium" 
                       style="background-color: <%= badge[:background_color] %>; color: <%= badge[:text_color] %>;">
                    <%= badge[:icon] %>
                  </div>
                  <div class="ml-3">
                    <div class="text-sm font-medium text-stone-900"><%= badge[:name] %></div>
                    <div class="text-sm text-stone-500"><%= truncate(badge[:description], length: 50) %></div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900"><%= metrics[:total_assignments] %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                <%= metrics[:total_clicks] %>
                <div class="text-xs text-stone-500"><%= metrics[:unique_clickers] %> unique</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                <%= metrics[:total_views] %>
                <div class="text-xs text-stone-500"><%= metrics[:unique_viewers] %> unique</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900"><%= metrics[:click_through_rate] %>%</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                <div><%= metrics[:avg_clicks_per_assignment] %> clicks/badge</div>
                <div class="text-xs text-stone-500"><%= metrics[:avg_views_per_assignment] %> views/badge</div>
              </td>
            </tr>
          <% end %>
          <% if @analytics[:badge_performance].empty? %>
            <tr>
              <td colspan="6" class="px-6 py-4 text-center text-sm text-stone-500">
                No badge performance data available for the selected period.
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Click Analytics and Top Performers Grid -->
  <div class="grid grid-cols-1 gap-8 mb-8 lg:grid-cols-2">
    <!-- Click Analytics -->
    <div class="click-analytics bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Click Analytics</h2>
        <p class="text-sm text-stone-500">Badge click patterns and contexts</p>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <h4 class="text-sm font-medium text-stone-700 mb-2">Clicks by Context</h4>
            <% @analytics[:click_analytics][:clicks_by_context].each do |context, count| %>
              <div class="flex items-center justify-between py-1">
                <span class="text-sm text-stone-600 capitalize"><%= context.humanize %></span>
                <span class="text-sm font-medium text-stone-900"><%= count %></span>
              </div>
            <% end %>
          </div>

          <div class="border-t border-stone-200 pt-4">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Authenticated Clicks</span>
              <span class="text-sm text-stone-900"><%= @analytics[:click_analytics][:authenticated_clicks] %></span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Anonymous Clicks</span>
              <span class="text-sm text-stone-900"><%= @analytics[:click_analytics][:anonymous_clicks] %></span>
            </div>
          </div>

          <% if @analytics[:click_analytics][:peak_click_hours].any? %>
            <div class="border-t border-stone-200 pt-4">
              <h4 class="text-sm font-medium text-stone-700 mb-2">Peak Click Hours</h4>
              <% @analytics[:click_analytics][:peak_click_hours].each do |hour_data| %>
                <div class="flex items-center justify-between py-1">
                  <span class="text-sm text-stone-600"><%= hour_data[:hour] %>:00</span>
                  <span class="text-sm font-medium text-stone-900"><%= hour_data[:count] %> clicks</span>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Top Performers -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Top Performers</h2>
        <p class="text-sm text-stone-500">Users with highest badge engagement</p>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <h4 class="text-sm font-medium text-stone-700 mb-2">Most Clicked Users</h4>
            <% if @analytics[:top_performers][:top_clicked_users].any? %>
              <% @analytics[:top_performers][:top_clicked_users].first(5).each do |user| %>
                <div class="flex items-center justify-between py-1">
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-stone-900 truncate"><%= user[:name] %></div>
                    <div class="text-xs text-stone-500 truncate"><%= user[:email] %></div>
                  </div>
                  <span class="text-sm font-medium text-stone-900 ml-2"><%= user[:clicks] %> clicks</span>
                </div>
              <% end %>
            <% else %>
              <p class="text-sm text-stone-500">No click data available</p>
            <% end %>
          </div>

          <div class="border-t border-stone-200 pt-4">
            <h4 class="text-sm font-medium text-stone-700 mb-2">Most Viewed Users</h4>
            <% if @analytics[:top_performers][:top_viewed_users].any? %>
              <% @analytics[:top_performers][:top_viewed_users].first(5).each do |user| %>
                <div class="flex items-center justify-between py-1">
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-stone-900 truncate"><%= user[:name] %></div>
                    <div class="text-xs text-stone-500 truncate"><%= user[:email] %></div>
                  </div>
                  <span class="text-sm font-medium text-stone-900 ml-2"><%= user[:views] %> views</span>
                </div>
              <% end %>
            <% else %>
              <p class="text-sm text-stone-500">No view data available</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  <div class="mb-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Recent Badge Activity</h2>
      <p class="text-sm text-stone-500">Latest badge assignments, clicks, and views</p>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Recent Assignments -->
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Recent Assignments</h4>
          <div class="space-y-2">
            <% @recent_badge_activity[:recent_assignments].each do |assignment| %>
              <div class="p-3 border border-stone-200 rounded-lg">
                <div class="flex items-center space-x-2">
                  <div class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium"
                       style="background-color: <%= assignment.badge_type.background_color %>; color: <%= assignment.badge_type.text_color %>;">
                    <%= assignment.badge_type.icon %>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-stone-900 truncate"><%= assignment.badge_type.name %></div>
                    <div class="text-xs text-stone-500">to <%= assignment.user.name %></div>
                    <div class="text-xs text-stone-400"><%= time_ago_in_words(assignment.created_at) %> ago</div>
                  </div>
                </div>
              </div>
            <% end %>
            <% if @recent_badge_activity[:recent_assignments].empty? %>
              <p class="text-sm text-stone-500">No recent assignments</p>
            <% end %>
          </div>
        </div>

        <!-- Recent Clicks -->
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Recent Clicks</h4>
          <div class="space-y-2">
            <% @recent_badge_activity[:recent_clicks].each do |click| %>
              <div class="p-3 border border-stone-200 rounded-lg">
                <div class="flex items-center space-x-2">
                  <div class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium"
                       style="background-color: <%= click.badge_type.background_color %>; color: <%= click.badge_type.text_color %>;">
                    <%= click.badge_type.icon %>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-stone-900 truncate"><%= click.badge_type.name %></div>
                    <div class="text-xs text-stone-500">on <%= click.badge_owner.name %></div>
                    <div class="text-xs text-stone-400"><%= time_ago_in_words(click.created_at) %> ago</div>
                  </div>
                </div>
              </div>
            <% end %>
            <% if @recent_badge_activity[:recent_clicks].empty? %>
              <p class="text-sm text-stone-500">No recent clicks</p>
            <% end %>
          </div>
        </div>

        <!-- Recent Views -->
        <div>
          <h4 class="text-sm font-medium text-stone-700 mb-3">Recent Views</h4>
          <div class="space-y-2">
            <% @recent_badge_activity[:recent_views].each do |view| %>
              <div class="p-3 border border-stone-200 rounded-lg">
                <div class="flex items-center space-x-2">
                  <div class="flex-shrink-0 w-6 h-6 rounded-full bg-stone-200 flex items-center justify-center">
                    <svg class="w-4 h-4 text-stone-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-stone-900 truncate"><%= view.viewed_user.name %></div>
                    <div class="text-xs text-stone-500"><%= pluralize(view.badge_types_displayed.length, 'badge') %> viewed</div>
                    <div class="text-xs text-stone-400"><%= time_ago_in_words(view.created_at) %> ago</div>
                  </div>
                </div>
              </div>
            <% end %>
            <% if @recent_badge_activity[:recent_views].empty? %>
              <p class="text-sm text-stone-500">No recent views</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
