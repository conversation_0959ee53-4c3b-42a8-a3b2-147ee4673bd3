module SuperAdmin
  class UserMasqueradeController < SuperAdmin::BaseController
    include Pagy::Backend

    def index
      @q = params[:q]
      @role_filter = params[:role_filter]
      @status_filter = params[:status_filter]
      @organization_filter = params[:organization_filter]
      @sort_by = params[:sort_by] || 'created_at'
      @sort_direction = params[:sort_direction] || 'desc'

      # Start with all users
      users = User.includes(:roles, :organizations)

      # Apply search filter
      if @q.present?
        users = users.where(
          "first_name ILIKE ? OR last_name ILIKE ? OR email ILIKE ? OR CAST(id AS TEXT) ILIKE ?",
          "%#{@q}%", "%#{@q}%", "%#{@q}%", "%#{@q}%"
        )
      end

      # Apply role filter
      if @role_filter.present? && @role_filter != 'all'
        if @role_filter == 'no_role'
          users = users.left_joins(:roles).where(roles: { id: nil })
        else
          users = users.joins(:roles).where(roles: { name: @role_filter })
        end
      end

      # Apply status filter
      if @status_filter.present? && @status_filter != 'all'
        case @status_filter
        when 'verified'
          users = users.where(verified: true)
        when 'unverified'
          users = users.where(verified: false)
        when 'onboarding_complete'
          users = users.where(onboarding_completed: true)
        when 'onboarding_incomplete'
          users = users.where(onboarding_completed: false)
        end
      end

      # Apply organization filter
      if @organization_filter.present? && @organization_filter != 'all'
        if @organization_filter == 'no_org'
          users = users.left_joins(:organizations).where(organizations: { id: nil })
        else
          users = users.joins(:organizations).where(organizations: { id: @organization_filter })
        end
      end

      # Apply sorting
      case @sort_by
      when 'name'
        users = users.order("first_name #{@sort_direction}, last_name #{@sort_direction}")
      when 'email'
        users = users.order("email #{@sort_direction}")
      when 'created_at'
        users = users.order("created_at #{@sort_direction}")
      else
        users = users.order("created_at #{@sort_direction}")
      end

      # Get filter options
      @available_roles = Role.pluck(:name).sort
      @available_organizations = Organization.select(:id, :name).order(:name)

      # Paginate results
      @pagy, @users = pagy(users, limit: 20)
    end
  end
end
