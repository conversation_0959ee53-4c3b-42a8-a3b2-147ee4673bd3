# frozen_string_literal: true

module Talent
  module Settings
    class SubscriptionsController < Talent::BaseController
      def show
        @subscription = Current.user.subscriptions.active.first # Get the first active subscription
        @loading = false

        if @subscription
          # Fetch additional subscription details
          fetch_subscription_details
          setup_billing_portal
        end
      end

      private

      def fetch_subscription_details
        # Get subscription details from Stripe if needed
        begin
          if @subscription.respond_to?(:processor) && @subscription.processor == :stripe && @subscription.processor_id.present?
            # Fetch additional details from Stripe API if needed
            # For now, we'll use the data from the Pay gem
            @next_billing_date = @subscription.ends_at || calculate_next_billing_date
            @billing_amount = fetch_billing_amount
          elsif @subscription.processor_id.present?
            # Handle case where subscription doesn't have processor method but has processor_id
            @next_billing_date = @subscription.ends_at || calculate_next_billing_date
            @billing_amount = fetch_billing_amount
          end
        rescue StandardError => e
          Rails.logger.error "Error fetching subscription details for user #{Current.user.id}: #{e.message}"
          # Continue with basic subscription info
        end
      end

      def setup_billing_portal
        # Ensure the default payment processor is set if needed (might be redundant if always Stripe)
        Current.user.set_payment_processor(:stripe) unless Current.user.payment_processor
        pay_customer = Current.user.payment_processor # Use the default payment processor

        if pay_customer&.respond_to?(:processor) && pay_customer.processor == :stripe && pay_customer.processor_id.present?
          begin
            # Use the existing pay_customer object directly
            @billing_portal_session = pay_customer.billing_portal(return_url: talent_settings_subscription_url)
            @billing_portal_url = @billing_portal_session.url
            Rails.logger.info "Successfully created billing portal session for user #{Current.user.id}"
          rescue Stripe::InvalidRequestError => e
            # Specific error for invalid Stripe requests (e.g., customer deleted)
            Rails.logger.error "Stripe InvalidRequestError creating Billing Portal session for user #{Current.user.id}, Pay::Customer #{pay_customer.id}, Stripe ID #{pay_customer.processor_id}: #{e.message}"
            handle_billing_portal_error("There was an issue accessing your billing details with Stripe (#{e.code}). Please contact support.")
          rescue Pay::Error => e
            # General Pay gem errors
            Rails.logger.error "Pay::Error creating Billing Portal session for user #{Current.user.id}, Pay::Customer #{pay_customer.id}: #{e.message}"
            handle_billing_portal_error("Could not access the billing portal due to a payment system error. Please try again later.")
          rescue StandardError => e
            # Catch other potential errors
            Rails.logger.error "Unexpected error creating Billing Portal session for user #{Current.user.id}: #{e.class} - #{e.message}\n#{e.backtrace.join("\n")}"
            handle_billing_portal_error("An unexpected error occurred while accessing the billing portal.")
          end
        elsif pay_customer&.processor_id.present?
          # Fallback for pay_customer without processor method but with processor_id
          begin
            @billing_portal_session = pay_customer.billing_portal(return_url: talent_settings_subscription_url)
            @billing_portal_url = @billing_portal_session.url
            Rails.logger.info "Successfully created billing portal session for user #{Current.user.id} (fallback method)"
          rescue StandardError => e
            Rails.logger.error "Error creating billing portal session (fallback): #{e.message}"
            handle_billing_portal_error("Could not access the billing portal. Please try again later.")
          end
        else
          # User has subscription but no valid Stripe Pay::Customer record
          Rails.logger.warn "User #{Current.user.id} has active subscription #{@subscription.id} but no valid Stripe Pay::Customer record."
          handle_billing_portal_error("Your payment processor details seem to be missing or invalid. Please contact support.")
        end
      end

      def handle_billing_portal_error(message)
        flash.now[:alert] = message
        @billing_portal_url = nil
        @billing_portal_error = true
      end

      def calculate_next_billing_date
        # Calculate next billing date based on subscription created date and billing cycle
        return nil unless @subscription.created_at

        # Assume monthly billing for now
        @subscription.created_at + 1.month
      end

      def fetch_billing_amount
        # This would typically come from Stripe API
        # For now, return a placeholder or fetch from subscription data
        case @subscription.processor_plan
        when 'price_1R9Q55DYYVPVcCCrWQOwsKmT'
          '$29.00' # Standard plan
        when 'price_1R9Q66DYYVPVcCCrnqiXNafF'
          '$99.00' # Premium plan
        else
          'See billing portal'
        end
      end
    end
  end
end
