require 'test_helper'

class AdminWorkflowTest < ActionDispatch::IntegrationTest
  setup do
    # Generate unique email suffix to avoid conflicts in parallel tests
    @email_suffix = Time.current.to_f.to_s.gsub('.', '')

    # Create roles
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @scout_role = Role.find_or_create_by!(name: 'scout')
    @talent_role = Role.find_or_create_by!(name: 'talent')

    # Create admin user
    @superadmin =
      User.create!(
        email: "superadmin_workflow#{@email_suffix}@test.com",
        password: 'password123123',
        verified: true,
        first_name: 'Super',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    # Create regular users
    @scout_user =
      User.create!(
        email: "scout_workflow#{@email_suffix}@test.com",
        password: 'password123123',
        verified: true,
        first_name: 'Scout',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @scout_user, role: @scout_role)

    @talent_user =
      User.create!(
        email: "talent_workflow#{@email_suffix}@test.com",
        password: 'password123123',
        verified: true,
        first_name: 'Talent',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @talent_user, role: @talent_role)

    # Create organization
    @organization = Organization.create!(name: 'Test Workflow Organization')
    [@superadmin, @scout_user, @talent_user].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member',
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end
  end

  test 'complete user management workflow' do
    sign_in_as(@superadmin)

    # 1. Access users index
    get super_admin_admin_users_path
    assert_response :success
    assert_select 'h1', text: 'Users'

    # 2. Search for specific user
    get super_admin_admin_users_path, params: { search: @scout_user.email }
    assert_response :success
    assert_select 'td', text: @scout_user.full_name

    # 3. View user details
    get super_admin_admin_user_path(@scout_user)
    assert_response :success
    assert_select 'h1', text: @scout_user.full_name

    # 4. Edit user
    get edit_super_admin_admin_user_path(@scout_user)
    assert_response :success
    assert_select 'form'

    # 5. Update user
    patch super_admin_admin_user_path(@scout_user),
          params: {
            user: {
              first_name: 'Updated Scout',
            },
          }
    assert_redirected_to super_admin_admin_user_path(@scout_user)

    # 6. Verify update
    @scout_user.reload
    assert_equal 'Updated Scout', @scout_user.first_name

    # 7. Export users data
    get super_admin_admin_users_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'complete job management workflow' do
    sign_in_as(@superadmin)

    # Create a job first
    job =
      Job.create!(
        title: 'Test Job Workflow',
        description: 'Test job description',
        category: 'social_media',
        social_media_goal: 'brand_awareness',
        budget_min: 1000,
        budget_max: 5000,
        status: 'active',
        user: @scout_user,
        organization: @organization,
      )

    # 1. Access jobs index
    get super_admin_admin_jobs_path
    assert_response :success
    assert_select 'h1', text: 'Jobs'

    # 2. Filter jobs by category
    get super_admin_admin_jobs_path, params: { category: 'social_media' }
    assert_response :success
    assert_select 'td', text: job.title

    # 3. View job details
    get super_admin_admin_job_path(job)
    assert_response :success
    assert_select 'h1', text: job.title

    # 4. Edit job
    get edit_super_admin_admin_job_path(job)
    assert_response :success
    assert_select 'form'

    # 5. Update job status
    patch super_admin_admin_job_path(job), params: { job: { status: 'paused' } }
    assert_redirected_to super_admin_admin_job_path(job)

    # 6. Verify update
    job.reload
    assert_equal 'paused', job.status
  end

  test 'complete organization management workflow' do
    sign_in_as(@superadmin)

    # 1. Access organizations index
    get super_admin_admin_organizations_path
    assert_response :success
    assert_select 'h1', text: 'Organizations'

    # 2. Search for organization
    get super_admin_admin_organizations_path,
        params: {
          search: @organization.name,
        }
    assert_response :success
    assert_select 'td', text: @organization.name

    # 3. View organization details
    get super_admin_admin_organization_path(@organization)
    assert_response :success
    assert_select 'h1', text: @organization.name

    # 4. Edit organization
    get edit_super_admin_admin_organization_path(@organization)
    assert_response :success
    assert_select 'form'

    # 5. Update organization
    patch super_admin_admin_organization_path(@organization),
          params: {
            organization: {
              description: 'Updated description',
            },
          }
    assert_redirected_to super_admin_admin_organization_path(@organization)

    # 6. Verify update
    @organization.reload
    assert_equal 'Updated description', @organization.description

    # 7. Create new organization
    get new_super_admin_admin_organization_path
    assert_response :success
    assert_select 'form'

    post super_admin_admin_organizations_path,
         params: {
           organization: {
             name: 'New Test Organization',
             description: 'A new organization for testing',
           },
         }
    assert_redirected_to super_admin_admin_organization_path(Organization.last)

    new_org = Organization.last
    assert_equal 'New Test Organization', new_org.name
  end

  test 'complete chat request monitoring workflow' do
    sign_in_as(@superadmin)

    # Create a chat request
    chat_request =
      ChatRequest.create!(
        scout: @scout_user,
        talent: @talent_user,
        status: 'pending',
        note: 'Test chat request for workflow',
      )

    # 1. Access chat requests index
    get super_admin_admin_chat_requests_path
    assert_response :success
    assert_select 'h1', text: 'Chat Requests'

    # 2. Filter by status
    get super_admin_admin_chat_requests_path, params: { status: 'pending' }
    assert_response :success
    assert_select 'td', text: 'Pending'

    # 3. View chat request details
    get super_admin_admin_chat_request_path(chat_request)
    assert_response :success
    assert_select 'h1', text: /Chat Request ##{chat_request.id}/

    # 4. Search by scout email
    get super_admin_admin_chat_requests_path,
        params: {
          search: @scout_user.email,
        }
    assert_response :success
    assert_select 'td', text: @scout_user.full_name
  end

  test 'complete conversation monitoring workflow' do
    sign_in_as(@superadmin)

    # Create a conversation with messages
    conversation =
      Conversation.create!(participants: [@scout_user, @talent_user])

    message1 =
      Message.create!(
        conversation: conversation,
        user: @scout_user,
        content: 'Hello from scout workflow',
      )

    message2 =
      Message.create!(
        conversation: conversation,
        user: @talent_user,
        content: 'Hello from talent workflow',
      )

    # 1. Access conversations index
    get super_admin_admin_conversations_path
    assert_response :success
    assert_select 'h1', text: 'Conversations'

    # 2. View conversation details
    get super_admin_admin_conversation_path(conversation)
    assert_response :success
    assert_select 'h1', text: /Conversation ##{conversation.id}/

    # 3. Verify message display
    assert_select '.message-content', text: message1.content
    assert_select '.message-content', text: message2.content

    # 4. Search conversations
    get super_admin_admin_conversations_path,
        params: {
          search: @scout_user.email,
        }
    assert_response :success
  end

  test 'complete audit log review workflow' do
    sign_in_as(@superadmin)

    # Create some audit logs by performing admin actions
    patch super_admin_admin_user_path(@scout_user),
          params: {
            user: {
              first_name: 'Audit Test',
            },
          }

    # 1. Access audit logs index
    get super_admin_admin_audit_logs_path
    assert_response :success
    assert_select 'h1', text: 'Audit Logs'

    # 2. Filter by action
    get super_admin_admin_audit_logs_path, params: { action: 'update' }
    assert_response :success

    # 3. Filter by admin user
    get super_admin_admin_audit_logs_path,
        params: {
          admin_user_id: @superadmin.id,
        }
    assert_response :success

    # 4. View specific audit log
    audit_log = AdminAuditLog.last
    get super_admin_admin_audit_log_path(audit_log)
    assert_response :success
    assert_select 'h1', text: /Audit Log ##{audit_log.id}/
  end

  test 'complete saved search workflow' do
    sign_in_as(@superadmin)

    # 1. Perform a search with filters
    get super_admin_admin_users_path,
        params: {
          search: 'scout',
          verified: 'true',
          role: 'scout',
        }
    assert_response :success

    # 2. Save the search
    post super_admin_saved_searches_path,
         params: {
           saved_search: {
             name: 'Active Scout Users',
             resource_type: 'AdminUser',
             search_params: {
               search: 'scout',
               verified: 'true',
               role: 'scout',
             }.to_json,
           },
         }
    assert_redirected_to super_admin_admin_users_path

    # 3. Access saved searches
    get super_admin_saved_searches_path
    assert_response :success

    # 4. Apply saved search
    saved_search = SavedSearch.last
    get super_admin_admin_users_path,
        params: {
          saved_search_id: saved_search.id,
        }
    assert_response :success
  end

  test 'complete dashboard overview workflow' do
    sign_in_as(@superadmin)

    # 1. Access admin dashboard
    get super_admin_admin_dashboard_path
    assert_response :success
    assert_select 'h1', text: 'Super Admin Dashboard'

    # 2. Verify analytics sections are present
    assert_select 'h2', text: 'Growth Trends'
    assert_select 'h2', text: 'User Engagement'
    assert_select 'h2', text: 'Conversion Rates'

    # 3. Verify navigation cards are present
    assert_select 'a[href*="admin_users"]', text: /Users/
    assert_select 'a[href*="admin_jobs"]', text: /Jobs/
    assert_select 'a[href*="admin_organizations"]', text: /Organizations/

    # 4. Verify recent activity sections
    assert_select 'h2', text: 'Recent Users'
    assert_select 'h2', text: 'Recent Jobs'
  end

  test 'complete bulk operations workflow' do
    sign_in_as(@superadmin)

    # Create multiple users for bulk operations
    users = []
    3.times do |i|
      user =
        User.create!(
          email: "bulk_user_#{i}@test.com",
          password: 'password123123',
          verified: false,
          first_name: "Bulk#{i}",
          last_name: 'User',
          onboarding_completed: true,
          onboarding_step: 'completed',
        )
      UserRole.create!(user: user, role: @scout_role)
      users << user
    end

    # 1. Access users index
    get super_admin_admin_users_path
    assert_response :success

    # 2. Select users for bulk operation (this would be done via JavaScript in real app)
    # For testing, we'll simulate the bulk update directly
    user_ids = users.map(&:id)

    # 3. Perform bulk verification
    patch super_admin_admin_users_path,
          params: {
            bulk_action: 'verify',
            user_ids: user_ids,
          }
    assert_redirected_to super_admin_admin_users_path

    # 4. Verify bulk operation worked
    users.each do |user|
      user.reload
      assert user.verified?
    end
  end

  private

  def sign_in_as(user)
    post sign_in_path, params: { email: user.email, password: 'password123123' }
    follow_redirect!
  end
end
