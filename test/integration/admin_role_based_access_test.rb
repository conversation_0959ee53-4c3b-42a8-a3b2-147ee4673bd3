require 'test_helper'

class AdminRoleBasedAccessTest < ActionDispatch::IntegrationTest
  setup do
    # Generate unique email suffix to avoid conflicts in parallel tests
    @email_suffix = Time.current.to_f.to_s.gsub('.', '')

    # Find or create roles
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @support_role = Role.find_or_create_by!(name: 'support')
    @readonly_role = Role.find_or_create_by!(name: 'readonly')

    # Create test users with different admin roles
    @superadmin =
      User.create!(
        email: "superadmin#{@email_suffix}@test.com",
        password: 'password123123',
        verified: true,
        first_name: '<PERSON>',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @support_admin =
      User.create!(
        email: "support#{@email_suffix}@test.com",
        password: 'password123123',
        verified: true,
        first_name: 'Support',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @support_admin, role: @support_role)

    @readonly_admin =
      User.create!(
        email: "readonly#{@email_suffix}@test.com",
        password: 'password123123',
        verified: true,
        first_name: 'ReadOnly',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @readonly_admin, role: @readonly_role)

    @regular_user =
      User.create!(
        email: "regular#{@email_suffix}@test.com",
        password: 'password123123',
        verified: true,
        first_name: 'Regular',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    # Create organization for users
    @organization = Organization.create!(name: 'Test Organization')
    [
      @superadmin,
      @support_admin,
      @readonly_admin,
      @regular_user,
    ].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member',
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end
  end

  test 'superadmin can access all admin interfaces' do
    sign_in_as(@superadmin)

    # Should be able to access admin dashboard
    get super_admin_admin_dashboard_path
    assert_response :success

    # Should be able to access users interface
    get super_admin_admin_users_path
    assert_response :success

    # Should be able to access edit user
    get edit_super_admin_admin_user_path(@regular_user)
    assert_response :success
  end

  test 'support admin has limited access' do
    sign_in_as(@support_admin)

    # Should be able to access admin dashboard
    get super_admin_admin_dashboard_path
    assert_response :success

    # Should be able to access users interface (read permission)
    get super_admin_admin_users_path
    assert_response :success

    # Should be able to edit users (has users_edit permission)
    get edit_super_admin_admin_user_path(@regular_user)
    assert_response :success
  end

  test 'readonly admin has read-only access' do
    sign_in_as(@readonly_admin)

    # Should be able to access admin dashboard
    get super_admin_admin_dashboard_path
    assert_response :success

    # Should be able to access users interface (read permission)
    get super_admin_admin_users_path
    assert_response :success

    # Should NOT be able to edit users (no users_edit permission)
    get edit_super_admin_admin_user_path(@regular_user)
    assert_redirected_to super_admin_root_path
    assert_match /insufficient permissions/i, flash[:alert]
  end

  test 'regular user cannot access admin interfaces' do
    sign_in_as(@regular_user)

    # Should NOT be able to access admin dashboard
    get super_admin_admin_dashboard_path
    assert_redirected_to root_path
    assert_match /administrative privileges required/i, flash[:alert]

    # Should NOT be able to access users interface
    get super_admin_admin_users_path
    assert_redirected_to root_path
    assert_match /administrative privileges required/i, flash[:alert]
  end

  test 'admin role methods work correctly' do
    assert_equal :superadmin, @superadmin.admin_role
    assert_equal 'Super Admin', @superadmin.admin_role_name
    assert @superadmin.can?(:users_edit)
    assert @superadmin.can?(:export_data)
    assert @superadmin.can_manage_roles?

    assert_equal :support, @support_admin.admin_role
    assert_equal 'Support Agent', @support_admin.admin_role_name
    assert @support_admin.can?(:users_edit)
    assert @support_admin.can?(:export_data)
    assert_not @support_admin.can_manage_roles?

    assert_equal :readonly, @readonly_admin.admin_role
    assert_equal 'Read-Only Admin', @readonly_admin.admin_role_name
    assert @readonly_admin.can?(:users_read)
    assert @readonly_admin.can?(:export_data)
    assert_not @readonly_admin.can?(:users_edit)
    assert_not @readonly_admin.can_manage_roles?

    assert_nil @regular_user.admin_role
    assert_nil @regular_user.admin_role_name
    assert_not @regular_user.can?(:users_read)
    assert_not @regular_user.can_access_admin?
  end

  private

  def sign_in_as(user)
    post sign_in_path, params: { email: user.email, password: 'password123123' }
    follow_redirect!
  end
end
