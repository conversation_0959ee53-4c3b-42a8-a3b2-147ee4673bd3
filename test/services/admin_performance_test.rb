# frozen_string_literal: true

require 'test_helper'

class AdminPerformanceTest < ActiveSupport::TestCase
  # Don't use fixtures to avoid foreign key issues
  self.use_transactional_tests = true

  # Override fixture loading
  def self.fixture_path
    nil
  end
  def setup
    # Clear caches before each test
    Rails.cache.clear
    AdminDashboardStatsService.clear_cache
    AdminRecentActivityService.clear_cache
    AdminFilterOptionsService.clear_all_caches
  end

  test 'dashboard stats service uses counter caches efficiently' do
    # Create test data without fixtures
    organization = Organization.create!(name: 'Test Org')
    user =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Test',
        last_name: 'User',
        verified: true,
      )

    job =
      Job.create!(
        title: 'Test Job',
        description: 'Test Description',
        organization: organization,
        job_category: :social_media,
        platform: :x_twitter,
        budget_range: :under_1000,
        work_duration: :short_term,
        outcome: :followers,
        status: :published,
      )

    # Create some test records with different users to avoid uniqueness validation
    3.times do |i|
      test_user =
        User.create!(
          email: "test#{i}@example.com",
          password: 'Secret1*3*5*',
          first_name: 'Test',
          last_name: "User#{i}",
          verified: true,
        )
      JobApplication.create!(
        job: job,
        user: test_user,
        status: :applied,
        application_letter: "Test application #{i}",
      )
    end

    conversation = Conversation.create!
    2.times do |i|
      Message.create!(
        conversation: conversation,
        user: user,
        body: "Test message #{i}",
      )
    end

    # Test that counter caches are working
    assert_equal 3, job.reload.job_applications_count
    assert_equal 2, conversation.reload.messages_count

    # Test dashboard stats generation
    stats = AdminDashboardStatsService.generate_stats

    assert stats[:applications][:total] >= 3
    assert stats[:communication][:messages] >= 2
    assert stats[:organizations][:total_jobs] >= 1
  end

  test 'filter options service caches efficiently' do
    # Clear cache first to ensure clean state
    AdminFilterOptionsService.clear_cache('job')

    # Test job filter options caching
    options1 = AdminFilterOptionsService.job_filter_options
    options2 = AdminFilterOptionsService.job_filter_options

    # Should return the same values (cached)
    assert_equal options1, options2
    assert options1.is_a?(Hash)
    assert options1.key?(:status)
    assert options1.key?(:job_category)

    # Test cache clearing
    AdminFilterOptionsService.clear_cache('job')
    options3 = AdminFilterOptionsService.job_filter_options

    # Should still return the same structure after cache clear
    assert_equal options1, options3
    assert options3.is_a?(Hash)
  end

  test 'recent activity service includes proper associations' do
    # Create test data without fixtures
    organization = Organization.create!(name: 'Test Org')
    user1 =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Test',
        last_name: 'User1',
        verified: true,
      )
    user2 =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Test',
        last_name: 'User2',
        verified: true,
      )

    job =
      Job.create!(
        title: 'Test Job',
        description: 'Test Description',
        organization: organization,
        job_category: :social_media,
        platform: :x_twitter,
        budget_range: :under_1000,
        work_duration: :short_term,
        outcome: :followers,
        status: :published,
      )

    # Create a job application
    JobApplication.create!(
      job: job,
      user: user1,
      status: :applied,
      application_letter: 'Test application',
    )

    # Create a chat request
    ChatRequest.create!(
      scout: user1,
      talent: user2,
      status: :pending,
      requested_at: Time.current,
    )

    activity = AdminRecentActivityService.generate_recent_activity

    assert activity.is_a?(Hash)
    assert activity.key?(:recent_applications)
    assert activity.key?(:recent_chat_requests)
  end

  test 'performance monitoring tracks query counts' do
    query_count = 0

    # Subscribe to SQL notifications to count queries
    subscription =
      ActiveSupport::Notifications.subscribe('sql.active_record') do |*args|
        query_count += 1
      end

    begin
      AdminPerformanceMonitorService.monitor_query('Test Query') do
        User.first
        Job.first
      end

      # Should have executed at least 2 queries
      assert query_count >= 2
    ensure
      ActiveSupport::Notifications.unsubscribe(subscription)
    end
  end

  test 'counter caches are maintained correctly' do
    # Create test data without fixtures
    organization = Organization.create!(name: 'Test Org Counter')
    user =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Counter',
        last_name: 'User',
        verified: true,
      )

    initial_job_count = organization.jobs_count || 0

    # Create a new job
    new_job =
      Job.create!(
        title: 'Test Job Counter',
        description: 'Test Description',
        organization: organization,
        job_category: :social_media,
        platform: :x_twitter,
        budget_range: :under_1000,
        work_duration: :short_term,
        outcome: :followers,
        status: :published,
      )

    # Counter cache should be updated
    assert_equal initial_job_count + 1, organization.reload.jobs_count

    # Create a job application
    JobApplication.create!(
      job: new_job,
      user: user,
      status: :applied,
      application_letter: 'Test application',
    )

    # Counter cache should be updated
    assert_equal 1, new_job.reload.job_applications_count
  end

  test 'cache invalidation works correctly' do
    # Generate initial stats (should cache)
    stats1 = AdminDashboardStatsService.generate_stats
    initial_job_count = stats1[:jobs][:total]

    # Create new data without fixtures
    organization = Organization.create!(name: 'Test Cache Org')
    Job.create!(
      title: 'Test Job for Cache',
      description: 'Test Description',
      organization: organization,
      job_category: :social_media,
      platform: :x_twitter,
      budget_range: :under_1000,
      work_duration: :short_term,
      outcome: :followers,
      status: :published,
    )

    # Cache should be automatically invalidated due to Job creation
    # (see config/initializers/admin_cache_invalidation.rb)
    stats2 = AdminDashboardStatsService.generate_stats
    assert stats2[:jobs][:total] > initial_job_count

    # Test manual cache clearing
    AdminDashboardStatsService.clear_cache
    stats3 = AdminDashboardStatsService.generate_stats

    # Stats should still be accurate after manual cache clear
    assert stats3[:jobs][:total] >= stats2[:jobs][:total]
  end
end
