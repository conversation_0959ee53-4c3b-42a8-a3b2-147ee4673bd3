require 'test_helper'

class SessionsControllerTest < ActionDispatch::IntegrationTest
  setup { @user = users(:lazaro_nixon) }

  test 'should get index' do
    sign_in_as @user

    get sessions_url
    assert_response :success
  end

  test 'should get new' do
    get sign_in_url
    assert_response :success
  end

  test 'should sign in' do
    post sign_in_url, params: { email: @user.email, password: 'Secret1*3*5*' }
    assert_redirected_to '/launchpad' # Changed from root_url and launchpad_url helper

    get root_url
    assert_response :success
  end

  test 'should not sign in with wrong credentials' do
    post sign_in_url, params: { email: @user.email, password: 'SecretWrong1*3' }
    assert_redirected_to sign_in_url(email_hint: @user.email)
    assert_equal 'That email or password is incorrect', flash[:alert]

    get root_url
    assert_redirected_to sign_in_url
  end

  test 'should sign out' do
    # Sign in the user, which creates a session
    sign_in_as @user

    # Get the session that was just created by the sign_in_as helper
    session = @user.sessions.order(created_at: :desc).first

    # Ensure we have a session
    assert_not_nil session, 'No session was created during sign in'

    delete session_url(session)
    assert_redirected_to sessions_url

    follow_redirect!
    assert_redirected_to sign_in_url
  end
end
