require 'test_helper'

class SuperAdmin::SavedSearchesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @support_role = Role.find_or_create_by!(name: 'support')
    @readonly_role = Role.find_or_create_by!(name: 'readonly')
    @scout_role = Role.find_or_create_by!(name: 'scout')

    timestamp = Time.current.to_f.to_s.gsub('.', '')

    @superadmin =
      User.create!(
        email: "superadmin#{timestamp}@example.com",
        password: 'Secret1*3*5*',
        verified: true,
        first_name: '<PERSON>',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @support_admin =
      User.create!(
        email: "support#{timestamp}@example.com",
        password: 'Secret1*3*5*',
        verified: true,
        first_name: 'Support',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @support_admin, role: @support_role)

    @regular_user =
      User.create!(
        email: "regular#{timestamp}@example.com",
        password: 'Secret1*3*5*',
        verified: true,
        first_name: 'Regular',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @regular_user, role: @scout_role)

    # Create test saved searches
    @user_search =
      SavedSearch.create!(
        user: @superadmin,
        name: 'Scout Users',
        resource_type: 'AdminUser',
        search_params: {
          'role' => 'scout',
          'verified' => 'true',
        },
      )

    @job_search =
      SavedSearch.create!(
        user: @superadmin,
        name: 'Recent Jobs',
        resource_type: 'AdminJob',
        search_params: {
          'created_range' => 'This Week',
        },
      )

    @other_user_search =
      SavedSearch.create!(
        user: @support_admin,
        name: 'Support Search',
        resource_type: 'AdminUser',
        search_params: {
          'search' => 'test',
        },
      )

    # Create organization for users
    @organization = Organization.create!(name: 'Test Organization')
    [@superadmin, @support_admin, @regular_user].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member',
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end
  end

  test 'superadmin can access saved searches index' do
    sign_in_as(@superadmin)

    get super_admin_saved_searches_path
    assert_response :success

    # Should see own saved searches
    assert_select 'h1', text: /saved searches/i
    assert_includes response.body, 'Scout Users'
    assert_includes response.body, 'Recent Jobs'

    # Should not see other users' searches
    assert_not_includes response.body, 'Support Search'
  end

  test 'support admin can access saved searches index' do
    sign_in_as(@support_admin)

    get super_admin_saved_searches_path
    assert_response :success

    # Should see own saved searches
    assert_includes response.body, 'Support Search'

    # Should not see other users' searches
    assert_not_includes response.body, 'Scout Users'
    assert_not_includes response.body, 'Recent Jobs'
  end

  test 'regular user cannot access saved searches' do
    sign_in_as(@regular_user)

    get super_admin_saved_searches_path
    assert_redirected_to root_path
    assert_equal 'Access denied. Admin privileges required.', flash[:alert]
  end

  test 'unauthenticated user cannot access saved searches' do
    get super_admin_saved_searches_path
    assert_redirected_to sign_in_path
  end

  test 'should show saved search and redirect to appropriate admin page' do
    sign_in_as(@superadmin)

    get super_admin_saved_search_path(@user_search)
    assert_redirected_to super_admin_admin_users_path(
                           role: 'scout',
                           verified: 'true',
                           advanced_search: 'true',
                         )
  end

  test 'should handle job searches correctly' do
    sign_in_as(@superadmin)

    get super_admin_saved_search_path(@job_search)
    assert_redirected_to super_admin_admin_jobs_path(
                           created_range: 'This Week',
                           advanced_search: 'true',
                         )
  end

  test 'should not allow access to other users saved searches' do
    sign_in_as(@support_admin)

    get super_admin_saved_search_path(@user_search)
    assert_response :not_found
  end

  test 'should delete saved search' do
    sign_in_as(@superadmin)

    assert_difference 'SavedSearch.count', -1 do
      delete super_admin_saved_search_path(@user_search)
    end

    assert_redirected_to super_admin_saved_searches_path
    assert_equal "Saved search 'Scout Users' for Users was deleted.",
                 flash[:notice]
  end

  test 'should not allow deleting other users saved searches' do
    sign_in_as(@support_admin)

    assert_no_difference 'SavedSearch.count' do
      delete super_admin_saved_search_path(@user_search)
    end

    assert_response :not_found
  end

  test 'should group searches by resource type in index' do
    sign_in_as(@superadmin)

    get super_admin_saved_searches_path
    assert_response :success

    # Check that searches are grouped
    response_body = response.body

    # Should have User section with user search
    assert_match /User.*Scout Users/m, response_body

    # Should have Job section with job search
    assert_match /Job.*Recent Jobs/m, response_body
  end

  test 'should handle empty saved searches gracefully' do
    # Delete all saved searches for superadmin
    @superadmin.saved_searches.destroy_all

    sign_in_as(@superadmin)

    get super_admin_saved_searches_path
    assert_response :success

    assert_match /no saved searches/i, response.body
  end

  test 'should display search descriptions' do
    sign_in_as(@superadmin)

    get super_admin_saved_searches_path
    assert_response :success

    # Should show search parameters in description
    assert_match /role.*scout/i, response.body
    assert_match /verified.*true/i, response.body
  end

  test 'should handle unknown resource types gracefully' do
    # Create a saved search with unknown resource type
    unknown_search =
      SavedSearch.create!(
        user: @superadmin,
        name: 'Unknown Search',
        resource_type: 'UnknownModel',
        search_params: {
          'search' => 'test',
        },
      )

    sign_in_as(@superadmin)

    # Should not raise an error when showing
    get super_admin_saved_search_path(unknown_search)
    assert_redirected_to super_admin_saved_searches_path
    assert_equal 'Unknown resource type: UnknownModel', flash[:alert]
  end

  test 'should preserve search parameters when redirecting' do
    search_with_complex_params =
      SavedSearch.create!(
        user: @superadmin,
        name: 'Complex Search',
        resource_type: 'AdminUser',
        search_params: {
          'search' => 'test user',
          'verified' => 'true',
          'role' => 'scout',
          'created_from' => '2024-01-01',
          'created_to' => '2024-12-31',
          'onboarding_completed' => 'true',
        },
      )

    sign_in_as(@superadmin)

    get super_admin_saved_search_path(search_with_complex_params)

    # Check that all parameters are preserved in redirect
    expected_params = {
      search: 'test user',
      verified: 'true',
      role: 'scout',
      created_from: '2024-01-01',
      created_to: '2024-12-31',
      onboarding_completed: 'true',
      advanced_search: 'true',
    }

    assert_redirected_to super_admin_admin_users_path(expected_params)
  end

  test 'should handle malformed search parameters' do
    # Create search with malformed JSON
    malformed_search =
      SavedSearch.new(
        user: @superadmin,
        name: 'Malformed Search',
        resource_type: 'AdminUser',
        search_params: nil,
      )
    malformed_search.save!(validate: false) # Skip validation to create malformed data

    sign_in_as(@superadmin)

    # Should handle gracefully without crashing
    get super_admin_saved_search_path(malformed_search)
    assert_redirected_to super_admin_admin_users_path(advanced_search: 'true')
  end
end
