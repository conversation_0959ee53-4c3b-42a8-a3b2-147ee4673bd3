require 'test_helper'

class SuperAdmin::AdminChatRequestsControllerTest < ActionDispatch::IntegrationTest
  setup do
    # Create roles
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @support_role = Role.find_or_create_by!(name: 'support')
    @readonly_role = Role.find_or_create_by!(name: 'readonly')
    @scout_role = Role.find_or_create_by!(name: 'scout')
    @talent_role = Role.find_or_create_by!(name: 'talent')

    # Create admin users
    @superadmin = User.create!(
      email: '<EMAIL>',
      password: 'Secret1*3*5*',
      verified: true,
      first_name: 'Super',
      last_name: 'Admin',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @support_admin = User.create!(
      email: '<EMAIL>',
      password: 'Secret1*3*5*',
      verified: true,
      first_name: 'Support',
      last_name: 'Admin',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @support_admin, role: @support_role)

    @readonly_admin = User.create!(
      email: '<EMAIL>',
      password: 'Secret1*3*5*',
      verified: true,
      first_name: 'ReadOnly',
      last_name: 'Admin',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @readonly_admin, role: @readonly_role)

    @scout_user = User.create!(
      email: '<EMAIL>',
      password: 'Secret1*3*5*',
      verified: true,
      first_name: 'Scout',
      last_name: 'User',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @scout_user, role: @scout_role)

    @talent_user = User.create!(
      email: '<EMAIL>',
      password: 'Secret1*3*5*',
      verified: true,
      first_name: 'Talent',
      last_name: 'User',
      onboarding_completed: true,
      onboarding_step: 'completed'
    )
    UserRole.create!(user: @talent_user, role: @talent_role)

    # Create organization
    @organization = Organization.create!(name: 'Test Chat Organization')
    [@superadmin, @support_admin, @readonly_admin, @scout_user, @talent_user].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member'
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end

    # Create test chat request
    @chat_request = ChatRequest.create!(
      scout: @scout_user,
      talent: @talent_user,
      status: 'pending',
      note: 'Test chat request note'
    )
  end

  test 'superadmin can access chat requests index' do
    sign_in_as(@superadmin)
    get super_admin_admin_chat_requests_path
    assert_response :success
    assert_select 'h1', text: 'Chat Requests'
  end

  test 'support admin can access chat requests index' do
    sign_in_as(@support_admin)
    get super_admin_admin_chat_requests_path
    assert_response :success
    assert_select 'h1', text: 'Chat Requests'
  end

  test 'readonly admin can access chat requests index' do
    sign_in_as(@readonly_admin)
    get super_admin_admin_chat_requests_path
    assert_response :success
    assert_select 'h1', text: 'Chat Requests'
  end

  test 'scout user cannot access chat requests index' do
    sign_in_as(@scout_user)
    get super_admin_admin_chat_requests_path
    assert_redirected_to root_path
  end

  test 'unauthenticated user cannot access chat requests index' do
    get super_admin_admin_chat_requests_path
    assert_redirected_to sign_in_path
  end

  test 'superadmin can view chat request details' do
    sign_in_as(@superadmin)
    get super_admin_admin_chat_request_path(@chat_request)
    assert_response :success
    assert_select 'h1', text: /Chat Request ##{@chat_request.id}/
  end

  test 'chat requests index displays search and filter options' do
    sign_in_as(@superadmin)
    get super_admin_admin_chat_requests_path
    assert_response :success
    
    assert_select 'input[name="search"]'
    assert_select 'select[name="status"]'
    assert_select 'button', text: 'Search'
    assert_select 'button', text: 'Clear'
  end

  test 'chat requests index search functionality' do
    sign_in_as(@superadmin)
    get super_admin_admin_chat_requests_path, params: { search: @scout_user.email }
    assert_response :success
    assert_select 'td', text: @scout_user.full_name
  end

  test 'chat requests index status filter' do
    sign_in_as(@superadmin)
    get super_admin_admin_chat_requests_path, params: { status: 'pending' }
    assert_response :success
    assert_select 'td', text: 'Pending'
  end

  test 'chat requests index displays empty state when no requests' do
    ChatRequest.destroy_all
    sign_in_as(@superadmin)
    get super_admin_admin_chat_requests_path
    assert_response :success
    assert_select '.empty-state', text: /No chat requests found/
  end

  test 'chat requests index pagination works' do
    # Create more chat requests to test pagination
    25.times do |i|
      talent = User.create!(
        email: "talent#{i}@test.com",
        password: 'Secret1*3*5*',
        verified: true,
        first_name: "Talent#{i}",
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed'
      )
      UserRole.create!(user: talent, role: @talent_role)
      
      ChatRequest.create!(
        scout: @scout_user,
        talent: talent,
        status: 'pending',
        note: "Test note #{i}"
      )
    end

    sign_in_as(@superadmin)
    get super_admin_admin_chat_requests_path
    assert_response :success
    assert_select '.pagination'
  end

  test 'export functionality for superadmin' do
    sign_in_as(@superadmin)
    get super_admin_admin_chat_requests_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'export functionality for support admin' do
    sign_in_as(@support_admin)
    get super_admin_admin_chat_requests_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'export functionality for readonly admin' do
    sign_in_as(@readonly_admin)
    get super_admin_admin_chat_requests_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'chat request show page displays all relevant information' do
    sign_in_as(@superadmin)
    get super_admin_admin_chat_request_path(@chat_request)
    assert_response :success
    
    assert_select 'dt', text: 'Scout'
    assert_select 'dd', text: @scout_user.full_name
    assert_select 'dt', text: 'Talent'
    assert_select 'dd', text: @talent_user.full_name
    assert_select 'dt', text: 'Status'
    assert_select 'dd', text: 'Pending'
    assert_select 'dt', text: 'Note'
    assert_select 'dd', text: @chat_request.note
  end

  test 'chat request show page displays timestamps' do
    sign_in_as(@superadmin)
    get super_admin_admin_chat_request_path(@chat_request)
    assert_response :success
    
    assert_select 'dt', text: 'Created At'
    assert_select 'dt', text: 'Updated At'
  end

  test 'chat requests index shows correct status badges' do
    # Create chat requests with different statuses
    accepted_request = ChatRequest.create!(
      scout: @scout_user,
      talent: @talent_user,
      status: 'accepted',
      note: 'Accepted request'
    )
    
    declined_request = ChatRequest.create!(
      scout: @scout_user,
      talent: @talent_user,
      status: 'declined',
      note: 'Declined request'
    )

    sign_in_as(@superadmin)
    get super_admin_admin_chat_requests_path
    assert_response :success
    
    assert_select '.badge', text: 'Pending'
    assert_select '.badge', text: 'Accepted'
    assert_select '.badge', text: 'Declined'
  end

  test 'date range filtering works' do
    sign_in_as(@superadmin)
    get super_admin_admin_chat_requests_path, params: {
      created_from: 1.day.ago.strftime('%Y-%m-%d'),
      created_to: 1.day.from_now.strftime('%Y-%m-%d')
    }
    assert_response :success
    assert_select 'td', text: @scout_user.full_name
  end

  private

  def sign_in_as(user)
    post sign_in_path, params: { email: user.email, password: 'Secret1*3*5*' }
    follow_redirect!
  end
end
