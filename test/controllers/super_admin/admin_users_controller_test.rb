require 'test_helper'

class SuperAdmin::AdminUsersControllerTest < ActionDispatch::IntegrationTest
  setup do
    # Generate unique email suffix to avoid conflicts in parallel tests
    @email_suffix = Time.current.to_f.to_s.gsub('.', '')

    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @support_role = Role.find_or_create_by!(name: 'support')
    @scout_role = Role.find_or_create_by!(name: 'scout')
    @talent_role = Role.find_or_create_by!(name: 'talent')

    @superadmin =
      User.create!(
        email: "superadmin#{@email_suffix}@example.com",
        password: 'Secret1*3*5*',
        verified: true,
        first_name: 'Super',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
        created_at: 1.week.ago,
      )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @scout_user =
      User.create!(
        email: "scout#{@email_suffix}@example.com",
        password: 'Secret1*3*5*',
        verified: false,
        first_name: 'Scout',
        last_name: 'User',
        onboarding_completed: false,
        onboarding_step: 'personal',
        created_at: 2.days.ago,
      )
    UserRole.create!(user: @scout_user, role: @scout_role)

    @talent_user =
      User.create!(
        email: "talent#{@email_suffix}@example.com",
        password: 'Secret1*3*5*',
        verified: true,
        first_name: 'Talent',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
        created_at: 1.day.ago,
      )
    UserRole.create!(user: @talent_user, role: @talent_role)

    # Create organization for users
    @organization = Organization.create!(name: 'Test Organization')
    [@superadmin, @scout_user, @talent_user].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member',
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end
  end

  test 'should get index with basic search' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path
    assert_response :success

    assert_select 'h1', text: 'Users'
    assert_select 'input[name="search"]'
    assert_select 'button', text: 'Advanced Search'
  end

  test 'should filter users by basic search' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path, params: { search: 'scout' }
    assert_response :success

    # Should include scout user
    assert_select 'td', text: @scout_user.email

    # Should not include other users in results
    response_body = response.body
    assert_not_includes response_body, @talent_user.email
    assert_not_includes response_body, @superadmin.email
  end

  test 'should show advanced search form when advanced_search=true' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path, params: { advanced_search: 'true' }
    assert_response :success

    # Should show advanced search form
    assert_select 'input[name="text_search"]'
    assert_select 'select[name="verified"]'
    assert_select 'select[name="role"]'
    assert_select 'input[name="created_from"]'
    assert_select 'input[name="created_to"]'
    assert_select 'button', text: 'Simple Search'
  end

  test 'should filter by verification status' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          advanced_search: 'true',
          verified: 'false',
        }
    assert_response :success

    # Should include unverified scout user
    assert_select 'td', text: @scout_user.email

    # Should not include verified users
    response_body = response.body
    assert_not_includes response_body, @talent_user.email
    assert_not_includes response_body, @superadmin.email
  end

  test 'should filter by role' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          advanced_search: 'true',
          role: 'scout',
        }
    assert_response :success

    # Should include scout user
    assert_select 'td', text: @scout_user.email

    # Should not include users with other roles
    response_body = response.body
    assert_not_includes response_body, @talent_user.email
    assert_not_includes response_body, @superadmin.email
  end

  test 'should filter by onboarding status' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          advanced_search: 'true',
          onboarding_completed: 'false',
        }
    assert_response :success

    # Should include incomplete scout user
    assert_select 'td', text: @scout_user.email

    # Should not include completed users
    response_body = response.body
    assert_not_includes response_body, @talent_user.email
    assert_not_includes response_body, @superadmin.email
  end

  test 'should filter by date range' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          advanced_search: 'true',
          created_from: 3.days.ago.to_date.to_s,
          created_to: Date.current.to_s,
        }
    assert_response :success

    # Should include recent users
    assert_select 'td', text: @scout_user.email
    assert_select 'td', text: @talent_user.email

    # Should not include older users
    response_body = response.body
    assert_not_includes response_body, @superadmin.email
  end

  test 'should filter by text search in advanced mode' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          advanced_search: 'true',
          text_search: 'talent',
        }
    assert_response :success

    # Should include talent user
    assert_select 'td', text: @talent_user.email

    # Should not include other users
    response_body = response.body
    assert_not_includes response_body, @scout_user.email
    assert_not_includes response_body, @superadmin.email
  end

  test 'should combine multiple filters' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          advanced_search: 'true',
          text_search: 'scout',
          verified: 'false',
          role: 'scout',
        }
    assert_response :success

    # Should include scout user that matches all criteria
    assert_select 'td', text: @scout_user.email

    # Should not include other users
    response_body = response.body
    assert_not_includes response_body, @talent_user.email
    assert_not_includes response_body, @superadmin.email
  end

  test 'should save search when requested' do
    sign_in_as(@superadmin)

    assert_difference 'SavedSearch.count', 1 do
      get super_admin_admin_users_path,
          params: {
            advanced_search: 'true',
            text_search: 'scout',
            verified: 'false',
            save_search: 'true',
            search_name: 'Unverified Scouts',
          }
    end

    assert_response :success

    saved_search = SavedSearch.last
    assert_equal @superadmin, saved_search.user
    assert_equal 'Unverified Scouts', saved_search.name
    assert_equal 'User', saved_search.resource_type
    assert_equal 'scout', saved_search.search_params['text_search']
    assert_equal 'false', saved_search.search_params['verified']
  end

  test 'should handle saved search application' do
    saved_search =
      SavedSearch.create!(
        user: @superadmin,
        name: 'Test Search',
        resource_type: 'AdminUser',
        search_params: {
          'role' => 'scout',
          'verified' => 'false',
        },
      )

    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          saved_search_id: saved_search.id,
        }
    assert_response :success

    # Should apply saved search parameters
    assert_select 'td', text: @scout_user.email

    response_body = response.body
    assert_not_includes response_body, @talent_user.email
    assert_not_includes response_body, @superadmin.email
  end

  test 'should export CSV with search filters' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          format: :csv,
          advanced_search: 'true',
          role: 'scout',
        }
    assert_response :success
    assert_equal 'text/csv', response.content_type

    # Should include CSV headers
    csv_content = response.body
    assert_includes csv_content, 'Email'
    assert_includes csv_content, 'First Name'
    assert_includes csv_content, 'Last Name'

    # Should include filtered data
    assert_includes csv_content, @scout_user.email
    assert_not_includes csv_content, @talent_user.email
  end

  test 'should handle invalid saved search gracefully' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          saved_search_id: 99_999, # Non-existent ID
        }
    assert_response :success

    # Should show all users when saved search not found
    assert_select 'td', text: @scout_user.email
    assert_select 'td', text: @talent_user.email
    assert_select 'td', text: @superadmin.email
  end

  test 'should show pagination with search results' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path, params: { search: 'scout' }
    assert_response :success

    # Should show pagination info
    assert_select 'p', text: /Showing \d+ to \d+ of \d+ users/
  end

  test 'should preserve search parameters in pagination links' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          advanced_search: 'true',
          role: 'scout',
          verified: 'false',
        }
    assert_response :success

    # Check that pagination links preserve search parameters
    assert_select 'a[href*="role=scout"]'
    assert_select 'a[href*="verified=false"]'
    assert_select 'a[href*="advanced_search=true"]'
  end

  test 'should show filter summary' do
    sign_in_as(@superadmin)

    get super_admin_admin_users_path,
        params: {
          advanced_search: 'true',
          role: 'scout',
        }
    assert_response :success

    # Should show active filters
    assert_select 'div', text: /Role:/
    assert_select 'span', text: 'Scout'
  end
end
