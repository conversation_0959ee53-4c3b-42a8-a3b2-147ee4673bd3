# frozen_string_literal: true

require 'test_helper'

class SuperAdmin::BadgeAnalyticsControllerTest < ActionDispatch::IntegrationTest
  def setup
    # Create admin user with superadmin role
    @admin =
      User.create!(
        first_name: 'Super',
        last_name: 'Admin',
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
      )

    # Create superadmin role if it doesn't exist
    superadmin_role = Role.find_or_create_by(name: 'superadmin')
    UserRole.find_or_create_by(user: @admin, role: superadmin_role)

    # Create test badge types
    @badge_type1 =
      BadgeType.create!(
        name: 'Expert Badge',
        description: 'For expert users',
        background_color: '#000000',
        text_color: '#FFFFFF',
        icon: 'star',
        priority: 1,
        active: true,
      )

    @badge_type2 =
      BadgeType.create!(
        name: 'Contributor Badge',
        description: 'For active contributors',
        background_color: '#000000',
        text_color: '#FFFFFF',
        icon: 'heart',
        priority: 2,
        active: true,
      )

    # Create test users
    @user1 =
      User.create!(
        first_name: 'Test',
        last_name: 'User1',
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
      )

    @user2 =
      User.create!(
        first_name: 'Test',
        last_name: 'User2',
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
      )

    # Create test data
    create_test_badge_data

    # Sign in as admin
    sign_in @admin
  end

  test 'should get index with default parameters' do
    get super_admin_badge_analytics_path

    assert_response :success
    assert_not_nil assigns(:analytics)
    assert_not_nil assigns(:date_range)
    assert_not_nil assigns(:badge_types)
    assert_equal 30.days, assigns(:date_range)
    assert_nil assigns(:badge_type_id)
  end

  test 'should get index with date range filter' do
    get super_admin_badge_analytics_path, params: { date_range: '7' }

    assert_response :success
    assert_equal 7.days, assigns(:date_range)
  end

  test 'should get index with badge type filter' do
    get super_admin_badge_analytics_path,
        params: {
          badge_type_id: @badge_type1.id,
        }

    assert_response :success
    assert_equal @badge_type1.id, assigns(:badge_type_id)
    assert_equal @badge_type1, assigns(:selected_badge_type)
  end

  test 'should get index with both filters' do
    get super_admin_badge_analytics_path,
        params: {
          date_range: '14',
          badge_type_id: @badge_type2.id,
        }

    assert_response :success
    assert_equal 14.days, assigns(:date_range)
    assert_equal @badge_type2.id, assigns(:badge_type_id)
  end

  test 'should handle invalid date range gracefully' do
    get super_admin_badge_analytics_path, params: { date_range: 'invalid' }

    assert_response :success
    assert_equal 30.days, assigns(:date_range) # Should default to 30 days
  end

  test 'should handle invalid badge type id gracefully' do
    get super_admin_badge_analytics_path, params: { badge_type_id: 'invalid' }

    assert_response :success
    assert_equal 0, assigns(:badge_type_id)
    assert_nil assigns(:selected_badge_type)
  end

  test 'should handle non-existent badge type id gracefully' do
    get super_admin_badge_analytics_path, params: { badge_type_id: 99_999 }

    assert_response :success
    assert_equal 99_999, assigns(:badge_type_id)
    assert_nil assigns(:selected_badge_type)
  end

  test 'should export CSV with default parameters' do
    get export_super_admin_badge_analytics_path, params: { format: :csv }

    assert_response :success
    assert_equal 'text/csv', response.content_type
    assert_match /attachment; filename="badge_analytics_/,
                 response.headers['Content-Disposition']
  end

  test 'should export CSV with filters' do
    get export_super_admin_badge_analytics_path,
        params: {
          format: :csv,
          date_range: '7',
          badge_type_id: @badge_type1.id,
        }

    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'CSV export should contain proper headers' do
    get export_super_admin_badge_analytics_path, params: { format: :csv }

    assert_response :success
    csv_content = response.body

    # Check for expected CSV headers
    assert_includes csv_content, 'Badge Type'
    assert_includes csv_content, 'Total Assignments'
    assert_includes csv_content, 'Total Clicks'
    assert_includes csv_content, 'Total Views'
    assert_includes csv_content, 'Unique Clickers'
    assert_includes csv_content, 'Unique Viewers'
    assert_includes csv_content, 'Click Through Rate'
  end

  test 'should require admin authentication' do
    sign_out @admin

    get super_admin_badge_analytics_path

    assert_redirected_to sign_in_path
  end

  test 'should require superadmin role' do
    # Create regular user
    regular_user =
      User.create!(
        name: 'Regular User',
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    sign_out @admin
    sign_in regular_user

    get super_admin_badge_analytics_path

    assert_response :forbidden
  end

  test 'analytics data should be properly structured' do
    get super_admin_badge_analytics_path

    assert_response :success

    analytics = assigns(:analytics)
    assert_not_nil analytics

    # Check overview data structure
    overview = analytics[:overview]
    assert_includes overview.keys, :total_badges_assigned
    assert_includes overview.keys, :total_badge_clicks
    assert_includes overview.keys, :total_badge_views
    assert_includes overview.keys, :unique_badge_clickers
    assert_includes overview.keys, :unique_badge_viewers

    # Check badge performance data structure
    performance = analytics[:badge_performance]
    assert performance.is_a?(Array)

    if performance.any?
      first_badge = performance.first
      assert_includes first_badge.keys, :badge_type
      assert_includes first_badge.keys, :metrics

      badge_type = first_badge[:badge_type]
      assert_includes badge_type.keys, :id
      assert_includes badge_type.keys, :name
      assert_includes badge_type.keys, :description

      metrics = first_badge[:metrics]
      assert_includes metrics.keys, :total_assignments
      assert_includes metrics.keys, :total_clicks
      assert_includes metrics.keys, :total_views
    end
  end

  test 'should set additional view variables' do
    get super_admin_badge_analytics_path

    assert_response :success
    assert_not_nil assigns(:total_badge_types)
    assert_not_nil assigns(:total_badge_assignments)
    assert_not_nil assigns(:recent_badge_activity)

    assert assigns(:total_badge_types) >= 0
    assert assigns(:total_badge_assignments) >= 0
    assert assigns(:recent_badge_activity).is_a?(Hash)
  end

  test 'should handle performance monitoring' do
    # This test ensures the performance monitoring doesn't break the request
    get super_admin_badge_analytics_path

    assert_response :success
    # If we get here, the performance monitoring worked without errors
  end

  private

  def create_test_badge_data
    # Create badge assignments
    BadgeAssignment.create!(
      user: @user1,
      badge_type: @badge_type1,
      admin: @admin,
      assigned_at: 2.days.ago,
      notes: 'Test assignment 1',
    )

    BadgeAssignment.create!(
      user: @user2,
      badge_type: @badge_type2,
      admin: @admin,
      assigned_at: 1.day.ago,
      notes: 'Test assignment 2',
    )

    # Create badge clicks
    BadgeClick.create!(
      badge_type: @badge_type1,
      badge_owner: @user1,
      clicker_user: @user2,
      click_context: 'profile',
      ip_address: '***********',
      created_at: 1.day.ago,
    )

    # Create badge views
    BadgeView.create!(
      viewed_user: @user1,
      viewer_user: @user2,
      badge_types_displayed: [@badge_type1.id],
      ip_address: '***********',
      created_at: 1.day.ago,
    )
  end

  def sign_in(user)
    post sign_in_path, params: { email: user.email, password: 'Secret1*3*5*' }
  end

  def sign_out(user)
    delete global_sign_out_path
  end
end
