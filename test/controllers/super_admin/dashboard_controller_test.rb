require 'test_helper'

class SuperAdmin::DashboardControllerTest < ActionDispatch::IntegrationTest
  setup do
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @support_role = Role.find_or_create_by!(name: 'support')
    @readonly_role = Role.find_or_create_by!(name: 'readonly')
    @scout_role = Role.find_or_create_by!(name: 'scout')

    @superadmin =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
        first_name: '<PERSON>',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @support_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
        first_name: 'Support',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @support_admin, role: @support_role)

    @readonly_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
        first_name: '<PERSON><PERSON><PERSON><PERSON>',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @readonly_admin, role: @readonly_role)

    @regular_user =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
        first_name: 'Regular',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @regular_user, role: @scout_role)
  end

  test 'superadmin can access dashboard' do
    sign_in_as(@superadmin)
    get super_admin_root_path
    assert_response :success
    assert_select 'h1', 'Super Admin Dashboard'
  end

  test 'support admin can access dashboard' do
    sign_in_as(@support_admin)
    get super_admin_root_path
    assert_response :success
    assert_select 'h1', 'Super Admin Dashboard'
  end

  test 'readonly admin can access dashboard' do
    sign_in_as(@readonly_admin)
    get super_admin_root_path
    assert_response :success
    assert_select 'h1', 'Super Admin Dashboard'
  end

  test 'regular user cannot access dashboard' do
    sign_in_as(@regular_user)
    get super_admin_root_path
    assert_redirected_to root_path
  end

  test 'unauthenticated user cannot access dashboard' do
    get super_admin_root_path
    assert_redirected_to sign_in_path
  end

  test 'dashboard displays analytics sections' do
    sign_in_as(@superadmin)
    get super_admin_root_path
    assert_response :success

    # Check for main analytics sections
    assert_select 'h2', 'Growth Trends'
    assert_select 'h2', 'User Engagement'
    assert_select 'h2', 'Conversion Rates'
    assert_select 'h2', "Today's Activity Summary"
    assert_select 'h2', 'Recent Users'
    assert_select 'h2', 'Recent Jobs'
    assert_select 'h2', 'System Health'
  end

  test 'dashboard displays user statistics' do
    sign_in_as(@superadmin)
    get super_admin_root_path
    assert_response :success

    # Should display total user count
    assert_select 'dt', 'Total Users'
    assert_select 'dd', text: User.count.to_s
  end

  test 'dashboard displays system health metrics' do
    sign_in_as(@superadmin)
    get super_admin_root_path
    assert_response :success

    # Check for system health indicators
    assert_select 'h2', 'System Health'
    assert_select 'div', text: /Database/
    assert_select 'div', text: /Search/
    assert_select 'div', text: /Cache/
    assert_select 'div', text: /Storage/
  end

  private

  def sign_in_as(user)
    post sign_in_path, params: { email: user.email, password: 'Secret1*3*5*' }
  end
end
