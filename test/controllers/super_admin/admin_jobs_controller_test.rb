require 'test_helper'

class SuperAdmin::AdminJobsControllerTest < ActionDispatch::IntegrationTest
  setup do
    # Create roles
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @support_role = Role.find_or_create_by!(name: 'support')
    @readonly_role = Role.find_or_create_by!(name: 'readonly')
    @scout_role = Role.find_or_create_by!(name: 'scout')

    # Create admin users
    @superadmin =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
        first_name: 'Super',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @superadmin, role: @superadmin_role)

    @support_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
        first_name: 'Support',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @support_admin, role: @support_role)

    @readonly_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
        first_name: 'ReadOnly',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @readonly_admin, role: @readonly_role)

    @regular_user =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        verified: true,
        first_name: 'Regular',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @regular_user, role: @scout_role)

    # Create organization
    @organization = Organization.create!(name: 'Test Jobs Organization')
    [
      @superadmin,
      @support_admin,
      @readonly_admin,
      @regular_user,
    ].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member',
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end

    # Create test jobs
    @job =
      Job.create!(
        title: 'Test Job',
        description: 'Test job description',
        job_category: 'social_media',
        status: 'draft',
        organization: @organization,
      )
  end

  test 'superadmin can access jobs index' do
    sign_in_as(@superadmin)
    get super_admin_admin_jobs_path
    assert_response :success
    assert_select 'h1', text: 'Jobs'
  end

  test 'support admin can access jobs index' do
    sign_in_as(@support_admin)
    get super_admin_admin_jobs_path
    assert_response :success
    assert_select 'h1', text: 'Jobs'
  end

  test 'readonly admin can access jobs index' do
    sign_in_as(@readonly_admin)
    get super_admin_admin_jobs_path
    assert_response :success
    assert_select 'h1', text: 'Jobs'
  end

  test 'regular user cannot access jobs index' do
    sign_in_as(@regular_user)
    get super_admin_admin_jobs_path
    assert_redirected_to root_path
  end

  test 'unauthenticated user cannot access jobs index' do
    get super_admin_admin_jobs_path
    assert_redirected_to sign_in_path
  end

  test 'superadmin can view job details' do
    sign_in_as(@superadmin)
    get super_admin_admin_job_path(@job)
    assert_response :success
    assert_select 'h1', text: @job.title
  end

  test 'superadmin can edit job' do
    sign_in_as(@superadmin)
    get edit_super_admin_admin_job_path(@job)
    assert_response :success
    assert_select 'form'
  end

  test 'support admin can edit job' do
    sign_in_as(@support_admin)
    get edit_super_admin_admin_job_path(@job)
    assert_response :success
    assert_select 'form'
  end

  test 'readonly admin cannot edit job' do
    sign_in_as(@readonly_admin)
    get edit_super_admin_admin_job_path(@job)
    assert_response :forbidden
  end

  test 'jobs index displays search and filter options' do
    sign_in_as(@superadmin)
    get super_admin_admin_jobs_path
    assert_response :success

    assert_select 'input[name="search"]'
    assert_select 'select[name="category"]'
    assert_select 'select[name="status"]'
    assert_select 'button', text: 'Search'
    assert_select 'button', text: 'Clear'
  end

  test 'jobs index search functionality' do
    sign_in_as(@superadmin)
    get super_admin_admin_jobs_path, params: { search: 'Test Job' }
    assert_response :success
    assert_select 'td', text: @job.title
  end

  test 'jobs index category filter' do
    sign_in_as(@superadmin)
    get super_admin_admin_jobs_path, params: { job_category: 'social_media' }
    assert_response :success
    assert_select 'td', text: @job.title
  end

  test 'jobs index status filter' do
    sign_in_as(@superadmin)
    get super_admin_admin_jobs_path, params: { status: 'active' }
    assert_response :success
    assert_select 'td', text: @job.title
  end

  test 'superadmin can update job' do
    sign_in_as(@superadmin)
    patch super_admin_admin_job_path(@job),
          params: {
            job: {
              title: 'Updated Job Title',
            },
          }
    assert_redirected_to super_admin_admin_job_path(@job)
    @job.reload
    assert_equal 'Updated Job Title', @job.title
  end

  test 'support admin can update job' do
    sign_in_as(@support_admin)
    patch super_admin_admin_job_path(@job),
          params: {
            job: {
              title: 'Updated by Support',
            },
          }
    assert_redirected_to super_admin_admin_job_path(@job)
    @job.reload
    assert_equal 'Updated by Support', @job.title
  end

  test 'readonly admin cannot update job' do
    sign_in_as(@readonly_admin)
    patch super_admin_admin_job_path(@job),
          params: {
            job: {
              title: 'Should not update',
            },
          }
    assert_response :forbidden
  end

  test 'jobs index displays empty state when no jobs' do
    Job.destroy_all
    sign_in_as(@superadmin)
    get super_admin_admin_jobs_path
    assert_response :success
    assert_select '.empty-state', text: /No jobs found/
  end

  test 'jobs index pagination works' do
    # Create more jobs to test pagination
    25.times do |i|
      Job.create!(
        title: "Job #{i}",
        description: "Description #{i}",
        job_category: 'social_media',
        status: 'draft',
        organization: @organization,
      )
    end

    sign_in_as(@superadmin)
    get super_admin_admin_jobs_path
    assert_response :success
    assert_select '.pagination'
  end

  test 'export functionality for superadmin' do
    sign_in_as(@superadmin)
    get super_admin_admin_jobs_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'export functionality for support admin' do
    sign_in_as(@support_admin)
    get super_admin_admin_jobs_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  test 'export functionality denied for readonly admin' do
    sign_in_as(@readonly_admin)
    get super_admin_admin_jobs_path, params: { format: 'csv' }
    assert_response :success
    assert_equal 'text/csv', response.content_type
  end

  private

  def sign_in_as(user)
    post sign_in_path, params: { email: user.email, password: 'Secret1*3*5*' }
    follow_redirect!
  end
end
