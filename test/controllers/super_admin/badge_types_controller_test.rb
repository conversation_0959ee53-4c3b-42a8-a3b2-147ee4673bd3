require 'test_helper'

class SuperAdmin::BadgeTypesControllerTest < ActionDispatch::IntegrationTest
  def setup
    # Disable color contrast validation for controller tests
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'false'

    # Clear existing data to avoid conflicts
    BadgeAssignment.delete_all
    BadgeType.delete_all

    # Create test admin user with proper roles
    @admin = users(:admin)
    @admin.add_role(:superadmin) unless @admin.has_role?(:superadmin)

    # Create test badge types
    @badge_type =
      BadgeType.create!(
        name: 'Test Badge',
        description: 'A test badge for testing',
        background_color: '#3B82F6',
        text_color: '#FFFFFF',
        icon: 'star',
        priority: 1,
        active: true,
      )

    @inactive_badge_type =
      BadgeType.create!(
        name: 'Inactive Badge',
        description: 'An inactive test badge',
        background_color: '#6B7280',
        text_color: '#FFFFFF',
        icon: 'circle',
        priority: 2,
        active: false,
      )

    # Create test user for badge assignments
    @user = users(:talent)

    # Create badge assignment to test deletion restrictions
    @badge_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type,
        user: @user,
        admin: @admin,
        assigned_at: Time.current,
      )

    # Sign in as admin
    sign_in_as @admin
  end

  def teardown
    # Restore color contrast validation
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'true'
  end

  test 'should get index' do
    get super_admin_badge_types_path
    assert_response :success
    assert_select 'h1', text: /Badge Types/i
    assert_select 'table'
  end

  test 'should get index with search' do
    get super_admin_badge_types_path, params: { q: 'Test' }
    assert_response :success
    assert_includes response.body, @badge_type.name
    assert_not_includes response.body, @inactive_badge_type.name
  end

  test 'should get index with active filter' do
    get super_admin_badge_types_path, params: { active: 'true' }
    assert_response :success
    assert_includes response.body, @badge_type.name
    assert_not_includes response.body, @inactive_badge_type.name
  end

  test 'should get index with inactive filter' do
    get super_admin_badge_types_path, params: { active: 'false' }
    assert_response :success
    assert_not_includes response.body, @badge_type.name
    assert_includes response.body, @inactive_badge_type.name
  end

  test 'should show badge type' do
    get super_admin_badge_type_path(@badge_type)
    assert_response :success
    assert_select 'h1', text: @badge_type.name
    assert_includes response.body, @badge_type.description
  end

  test 'should get new' do
    get new_super_admin_badge_type_path
    assert_response :success
    assert_select 'form'
    assert_select 'input[name="badge_type[name]"]'
    assert_select 'textarea[name="badge_type[description]"]'
  end

  test 'should create badge type' do
    assert_difference('BadgeType.count') do
      post super_admin_badge_types_path,
           params: {
             badge_type: {
               name: 'New Badge',
               description: 'A new test badge',
               background_color: '#10B981',
               text_color: '#FFFFFF',
               icon: 'check',
               priority: 3,
               active: true,
             },
           }
    end

    badge_type = BadgeType.find_by(name: 'New Badge')
    assert_redirected_to super_admin_badge_type_path(badge_type)
    assert_equal 'Badge type was successfully created.', flash[:notice]

    # Verify audit log was created
    audit_log =
      AdminAuditLog.where(
        action: 'badge_type_created',
        resource: badge_type,
        admin_user: @admin,
      ).first
    assert_not_nil audit_log
  end

  test 'should not create badge type with invalid params' do
    assert_no_difference('BadgeType.count') do
      post super_admin_badge_types_path,
           params: {
             badge_type: {
               name: '', # Invalid - required field
               description: 'A test badge',
               background_color: '#10B981',
               text_color: '#FFFFFF',
               icon: 'check',
             },
           }
    end

    assert_response :unprocessable_entity
    assert_select '.error', text: /Name can't be blank/i

    # Verify failure audit log was created
    audit_log =
      AdminAuditLog.where(
        action: 'badge_type_create_failed',
        admin_user: @admin,
      ).first
    assert_not_nil audit_log
  end

  test 'should get edit' do
    get edit_super_admin_badge_type_path(@badge_type)
    assert_response :success
    assert_select 'form'
    assert_select 'input[value=?]', @badge_type.name
  end

  test 'should update badge type' do
    patch super_admin_badge_type_path(@badge_type),
          params: {
            badge_type: {
              name: 'Updated Badge Name',
              description: 'Updated description',
            },
          }

    @badge_type.reload
    assert_equal 'Updated Badge Name', @badge_type.name
    assert_equal 'Updated description', @badge_type.description
    assert_redirected_to super_admin_badge_type_path(@badge_type)
    assert_equal 'Badge type was successfully updated.', flash[:notice]

    # Verify audit log was created
    audit_log =
      AdminAuditLog.where(
        action: 'badge_type_updated',
        resource: @badge_type,
        admin_user: @admin,
      ).first
    assert_not_nil audit_log
  end

  test 'should not update badge type with invalid params' do
    original_name = @badge_type.name

    patch super_admin_badge_type_path(@badge_type),
          params: {
            badge_type: {
              name: '', # Invalid
              background_color: 'invalid-color', # Invalid
            },
          }

    @badge_type.reload
    assert_equal original_name, @badge_type.name
    assert_response :unprocessable_entity
    assert_select '.error'

    # Verify failure audit log was created
    audit_log =
      AdminAuditLog.where(
        action: 'badge_type_update_failed',
        resource: @badge_type,
        admin_user: @admin,
      ).first
    assert_not_nil audit_log
  end

  test 'should not destroy badge type with assignments' do
    assert_no_difference('BadgeType.count') do
      delete super_admin_badge_type_path(@badge_type)
    end

    assert_redirected_to super_admin_badge_type_path(@badge_type)
    assert_match /Cannot delete badge type that is currently assigned/,
                 flash[:alert]

    # Verify blocked deletion audit log was created
    audit_log =
      AdminAuditLog.where(
        action: 'badge_type_delete_blocked',
        resource: @badge_type,
        admin_user: @admin,
      ).first
    assert_not_nil audit_log
  end

  test 'should destroy badge type without assignments' do
    # Remove the assignment first
    @badge_assignment.destroy

    assert_difference('BadgeType.count', -1) do
      delete super_admin_badge_type_path(@badge_type)
    end

    assert_redirected_to super_admin_badge_types_path
    assert_match /Badge type .* was successfully deleted/, flash[:notice]

    # Verify deletion audit log was created
    audit_log =
      AdminAuditLog.where(action: 'badge_type_deleted', admin_user: @admin)
        .first
    assert_not_nil audit_log
  end

  test 'should handle not found badge type' do
    get super_admin_badge_type_path(id: 99_999)
    assert_redirected_to super_admin_badge_types_path
    assert_match /Badge type not found/, flash[:alert]
  end

  test 'should export badge types as CSV' do
    get super_admin_badge_types_path, params: { format: :csv }
    assert_response :success
    assert_equal 'text/csv', response.content_type
    assert_includes response.body, @badge_type.name
  end

  test 'should require admin access' do
    sign_out @admin
    regular_user = users(:talent)
    sign_in_as regular_user

    get super_admin_badge_types_path
    assert_redirected_to onboarding_personal_url
    assert_match /Please complete your profile setup to continue/, flash[:alert]
  end

  test 'should require superadmin for destroy action' do
    # Create admin without superadmin role
    regular_admin = users(:scout)
    regular_admin.add_role(:admin)
    sign_out @admin
    sign_in_as regular_admin

    delete super_admin_badge_type_path(@badge_type)
    assert_redirected_to root_path
    assert_match /Access denied. Administrative privileges required/,
                 flash[:alert]
  end
end
