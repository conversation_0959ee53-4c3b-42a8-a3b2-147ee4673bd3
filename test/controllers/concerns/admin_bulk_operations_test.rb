require 'test_helper'

class AdminBulkOperationsTest < ActionDispatch::IntegrationTest
  def setup
    # Create admin user with superadmin role
    @admin_user =
      User.create!(
        email: '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User',
        password: 'Secret1*3*5*',
        verified: true,
        onboarding_completed: true,
      )

    # Assign superadmin role
    superadmin_role = Role.find_or_create_by(name: 'superadmin')
    unless @admin_user.has_role?(:superadmin)
      @admin_user.roles << superadmin_role
    end

    sign_in_as @admin_user

    # Create test users
    @user1 =
      User.create!(
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User1',
        password: 'Secret1*3*5*',
        verified: false,
        onboarding_completed: false,
      )

    @user2 =
      User.create!(
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User2',
        password: 'Secret1*3*5*',
        verified: false,
        onboarding_completed: false,
      )
  end

  test 'bulk update users with verification' do
    post bulk_update_super_admin_admin_users_path,
         params: {
           ids: "#{@user1.id},#{@user2.id}",
           operation_key: 'verify_users',
         }

    assert_response :success

    response_data = JSON.parse(response.body)
    assert_equal 2, response_data['success_count']
    assert_equal 0, response_data['failed_count']

    # Verify users were actually updated
    @user1.reload
    @user2.reload
    assert @user1.verified?
    assert @user2.verified?
  end

  test 'bulk update users with onboarding completion' do
    post bulk_update_super_admin_admin_users_path,
         params: {
           ids: "#{@user1.id},#{@user2.id}",
           operation_key: 'complete_onboarding',
         }

    assert_response :success

    response_data = JSON.parse(response.body)
    assert_equal 2, response_data['success_count']
    assert_equal 0, response_data['failed_count']

    # Verify users were actually updated
    @user1.reload
    @user2.reload
    assert @user1.onboarding_completed?
    assert @user2.onboarding_completed?
  end

  test 'bulk delete users' do
    user_ids = [@user1.id, @user2.id]

    post bulk_delete_super_admin_admin_users_path,
         params: {
           ids: "#{@user1.id},#{@user2.id}",
         }

    assert_response :success

    response_data = JSON.parse(response.body)
    assert_equal 2, response_data['success_count']
    assert_equal 0, response_data['failed_count']

    # Verify users were actually deleted
    assert_nil User.find_by(id: @user1.id)
    assert_nil User.find_by(id: @user2.id)
  end

  test 'bulk update with no records selected returns error' do
    post bulk_update_super_admin_admin_users_path,
         params: {
           ids: '',
           operation_key: 'verify_users',
         }

    assert_response :bad_request

    response_data = JSON.parse(response.body)
    assert_equal 'No records selected', response_data['error']
  end

  test 'bulk update with invalid operation key returns error' do
    post bulk_update_super_admin_admin_users_path,
         params: {
           ids: "#{@user1.id}",
           operation_key: 'invalid_operation',
         }

    assert_response :bad_request

    response_data = JSON.parse(response.body)
    assert_equal 'Invalid bulk operation', response_data['error']
  end

  test 'bulk operations require admin permissions' do
    # Create a regular user without admin role
    regular_user =
      User.create!(
        email: '<EMAIL>',
        first_name: 'Regular',
        last_name: 'User',
        password: 'Secret1*3*5*',
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    sign_in_as regular_user

    post bulk_update_super_admin_admin_users_path,
         params: {
           ids: "#{@user1.id}",
           operation_key: 'verify_users',
         }

    assert_response :forbidden
  end

  test 'bulk operations create audit log entries' do
    assert_difference 'AdminAuditLog.count', 1 do
      post bulk_update_super_admin_admin_users_path,
           params: {
             ids: "#{@user1.id},#{@user2.id}",
             operation_key: 'verify_users',
           }
    end

    audit_log = AdminAuditLog.last
    assert_equal @admin_user, audit_log.admin_user
    assert_equal 'update', audit_log.action
    assert_equal 'user', audit_log.resource_type
    assert_equal 2, audit_log.change_data['count'][1]
  end

  private

  def teardown
    # Clean up test users
    [@user1, @user2].each { |user| user&.destroy if user&.persisted? }
  end
end
