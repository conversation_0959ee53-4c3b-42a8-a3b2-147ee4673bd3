require 'application_system_test_case'

class JobShowPageTest < ApplicationSystemTestCase
  # Don't load fixtures to avoid foreign key issues
  self.use_transactional_tests = false

  def self.fixtures(*args)
    # Override to prevent fixture loading
  end

  setup do
    # Create a test user with scout privileges
    @user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Test',
        last_name: 'Scout',
        time_zone: 'UTC',
        onboarding_completed: true,
        onboarding_step: 'completed',
        signup_intent: 'scout',
        scout_signup_completed: true,
        talent_signup_completed: false,
      )

    # Create an organization for the scout
    @organization = Organization.create!(name: 'Test Organization')

    # Add scout to organization
    OrganizationMembership.create!(
      user: @user,
      organization: @organization,
      org_role: 'admin',
    )

    @user.update!(last_logged_in_organization_id: @organization.id)
  end

  teardown do
    # Clean up test data
    Job.delete_all
    OrganizationMembership.delete_all
    Organization.delete_all
    User.delete_all
  end

  test 'displays social media category-specific attributes' do
    # Create a social media job with category-specific attributes
    job =
      @organization.jobs.create!(
        title: 'Social Media Manager',
        description: 'Looking for a social media ghostwriter',
        job_category: 'social_media',
        platform: 'linkedin',
        outcome: 'leads',
        social_media_goal_type: 'social_media_leads',
        social_media_understands_risk_acknowledged: true,
        budget_range: 'range_1000_2000',
        work_duration: 'short_term',
        status: 'published',
      )

    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Log in'

    # Visit the job show page
    visit scout_job_path(job)

    # Verify basic job information is displayed
    assert_text 'Social Media Manager'
    assert_text 'Social media'

    # Verify category-specific details section exists
    assert_text 'Category-Specific Details'
    assert_text 'Details specific to the social media category'

    # Verify social media specific attributes are displayed
    assert_text 'Platform'
    assert_text 'Linkedin'
    assert_text 'Social Media Goal Type'
    assert_text 'Social media leads'
    assert_text 'Risk Acknowledgment'
    assert_text 'Acknowledged'
  end

  test 'displays newsletter category-specific attributes' do
    # Create a newsletter job with category-specific attributes
    job =
      @organization.jobs.create!(
        title: 'Newsletter Writer',
        description: 'Looking for a newsletter ghostwriter',
        job_category: 'newsletter',
        outcome: 'grow_email_list',
        newsletter_frequency: 'weekly',
        newsletter_length: 'words_300_600',
        budget_range: 'range_1000_2000',
        work_duration: 'long_term',
        status: 'published',
      )

    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'Secret1*3*5*'
    click_button 'Log in'

    # Visit the job show page
    visit scout_job_path(job)

    # Verify basic job information is displayed
    assert_text 'Newsletter Writer'
    assert_text 'Newsletter'

    # Verify category-specific details section exists
    assert_text 'Category-Specific Details'
    assert_text 'Details specific to the newsletter category'

    # Verify newsletter specific attributes are displayed
    assert_text 'Newsletter Frequency'
    assert_text 'Weekly'
    assert_text 'Newsletter Length'
    assert_text 'Words 300 600'
  end

  test 'displays lead magnet category-specific attributes' do
    # Create a lead magnet job with category-specific attributes
    job =
      @organization.jobs.create!(
        title: 'Lead Magnet Creator',
        description: 'Looking for a lead magnet ghostwriter',
        job_category: 'lead_magnet',
        outcome: 'grow_email_list',
        lead_magnet_type: 'ebook',
        budget_range: 'range_1000_2000',
        work_duration: 'one_time_project',
        status: 'published',
      )

    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Log in'

    # Visit the job show page
    visit scout_job_path(job)

    # Verify basic job information is displayed
    assert_text 'Lead Magnet Creator'
    assert_text 'Lead magnet'

    # Verify category-specific details section exists
    assert_text 'Category-Specific Details'
    assert_text 'Details specific to the lead magnet category'

    # Verify lead magnet specific attributes are displayed
    assert_text 'Lead Magnet Type'
    assert_text 'Ebook'
  end

  test 'handles missing category gracefully' do
    # Create a job without a category
    job =
      @organization.jobs.create!(
        title: 'Generic Job',
        description: 'A job without specific category',
        budget_range: 'range_1000_2000',
        work_duration: 'short_term',
        status: 'draft',
      )

    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Log in'

    # Visit the job show page
    visit scout_job_path(job)

    # Verify basic job information is displayed
    assert_text 'Generic Job'

    # Verify category-specific details section exists with fallback
    assert_text 'Category-Specific Details'
    assert_text 'Details specific to the selected category'
    assert_text 'No category-specific details available'
  end

  test 'displays additional client information fields when present' do
    # Create a job with additional client information
    job =
      @organization.jobs.create!(
        title: 'Content Writer',
        description: 'Looking for a content writer',
        job_category: 'social_media',
        platform: 'linkedin',
        outcome: 'leads',
        social_media_goal_type: 'social_media_leads',
        social_media_understands_risk_acknowledged: true,
        budget_range: 'range_1000_2000',
        work_duration: 'short_term',
        target_audience_description: 'Tech entrepreneurs and startup founders',
        emulated_brands_description:
          'HubSpot, Salesforce, and similar B2B companies',
        requirements: 'Must have experience with B2B content',
        status: 'published',
      )

    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Log in'

    # Visit the job show page
    visit scout_job_path(job)

    # Verify additional fields are displayed
    assert_text 'Requirements'
    assert_text 'Must have experience with B2B content'
    assert_text 'Target Audience'
    assert_text 'Tech entrepreneurs and startup founders'
    assert_text 'Brands to Emulate'
    assert_text 'HubSpot, Salesforce, and similar B2B companies'
  end
end
