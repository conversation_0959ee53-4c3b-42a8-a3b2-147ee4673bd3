# Searchkick configuration for different environments
if Rails.env.test?
  # In test environment, configure Searchkick to handle index conflicts
  # Use environment variables to configure Searchkick with worker isolation
  worker_id = ENV['TEST_ENV_NUMBER'] || '0'
  ENV['SEARCHKICK_INDEX_PREFIX'] = "test_#{Rails.env}_worker_#{worker_id}"

  # Set shorter timeouts for tests
  ENV['SEARCHKICK_TIMEOUT'] = '5'
  ENV['SEARCHKICK_SEARCH_TIMEOUT'] = '3'
  
  # Add a test helper method to manually reindex when needed
  module SearchkickTestHelpers
    def reindex_all_searchkick_models
      return unless defined?(Searchkick)

      searchable_models = [
        TalentProfile,
        Job,
        JobApplication,
        Conversation
      ]

      searchable_models.each do |model|
        begin
          # Ensure index exists before reindexing
          unless model.searchkick_index.exists?
            model.searchkick_index.create
          end
          # Reindex with data
          model.reindex
        rescue => e
          Rails.logger.warn "[SEARCHKICK] Failed to reindex #{model.name}: #{e.message}"
        end
      end
    end

    def ensure_searchkick_indexes_exist
      return unless defined?(Searchkick)

      searchable_models = [
        TalentProfile,
        Job,
        JobApplication,
        Conversation
      ]

      searchable_models.each do |model|
        begin
          # Use a more robust approach for parallel tests
          index = model.searchkick_index

          # Try to create index if it doesn't exist
          unless index.exists?
            index.create
            # Wait a moment for index to be ready
            sleep 0.1
          end

          # Verify index is accessible
          index.refresh
        rescue Elastic::Transport::Transport::Errors::NotFound => e
          # Index was deleted between check and use, recreate it
          Rails.logger.warn "[SEARCHKICK] Index not found for #{model.name}, recreating: #{e.message}"
          begin
            index.create
            sleep 0.1
            index.refresh
          rescue => create_error
            Rails.logger.error "[SEARCHKICK] Failed to recreate index for #{model.name}: #{create_error.message}"
          end
        rescue => e
          Rails.logger.warn "[SEARCHKICK] Failed to ensure index for #{model.name}: #{e.message}"
        end
      end
    end
    
    def clean_searchkick_indexes
      return unless defined?(Searchkick)
      
      searchable_models = [
        TalentProfile,
        Job,
        JobApplication,
        Conversation
      ]
      
      searchable_models.each do |model|
        begin
          model.searchkick_index.delete if model.searchkick_index.exists?
        rescue => e
          Rails.logger.warn "[SEARCHKICK] Failed to clean index for #{model.name}: #{e.message}"
        end
      end
    end
  end
  
  # Include the helper in test classes
  ActiveSupport::TestCase.include SearchkickTestHelpers if defined?(ActiveSupport::TestCase)
  
elsif Rails.env.development?
  # Development environment configuration
  ENV['SEARCHKICK_INDEX_PREFIX'] = "dev_#{Rails.env}"
  ENV['SEARCHKICK_TIMEOUT'] = '10'
  ENV['SEARCHKICK_SEARCH_TIMEOUT'] = '5'

elsif Rails.env.production?
  # Production environment configuration
  ENV['SEARCHKICK_TIMEOUT'] = '15'
  ENV['SEARCHKICK_SEARCH_TIMEOUT'] = '10'
end

# Configure Searchkick client options to suppress warnings
if defined?(Searchkick)
  # Suppress Elasticsearch client verification warnings
  Searchkick.client_options = {
    transport_options: {
      ssl: { verify: false }
    },
    log: false
  }
end

Rails.logger.info "[SEARCHKICK] Configured for #{Rails.env} environment"
