# Admin Interface Technical Documentation

## Architecture Overview

The Ghostwrote admin interface is a custom-built Rails application that provides comprehensive administrative functionality for managing users, jobs, organizations, and platform operations.

### Core Components

#### 1. Authentication & Authorization

- **Base Controller**: `SuperAdmin::BaseController`
- **Authentication**: Integration with Authentication Zero system
- **Authorization**: Role-based access control (superadmin, support, readonly)
- **Current Context**: Uses `Current.user` pattern for request-scoped user context

#### 2. Audit Logging System

- **Model**: `AdminAuditLog`
- **Functionality**: Tracks all administrative actions with full change history
- **Storage**: Uses `change_data` column (JSON) to avoid ActiveRecord conflicts
- **Integration**: Automatic logging in all admin controllers

#### 3. Admin Controllers

```
SuperAdmin::
├── BaseController (authentication, authorization, audit logging)
├── DashboardController (overview statistics and navigation)
├── AdminUsersController (user management)
├── AdminJobsController (job management)
├── AdminOrganizationsController (organization management)
├── AdminChatRequestsController (chat request monitoring)
├── AdminConversationsController (conversation management)
├── AdminAuditLogsController (audit log viewing)
└── [Additional resource controllers...]
```

## Database Schema

### Admin Audit Logs

```sql
CREATE TABLE admin_audit_logs (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  action VARCHAR NOT NULL,
  resource_type VARCHAR,
  resource_id BIGINT,
  change_data JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL
);

-- Indexes for performance
CREATE INDEX idx_admin_audit_logs_user_id ON admin_audit_logs(user_id);
CREATE INDEX idx_admin_audit_logs_resource ON admin_audit_logs(resource_type, resource_id);
CREATE INDEX idx_admin_audit_logs_created_at ON admin_audit_logs(created_at);
CREATE INDEX idx_admin_audit_logs_action ON admin_audit_logs(action);
```

### User Roles

```sql
-- Users table includes admin role enum
ALTER TABLE users ADD COLUMN role VARCHAR DEFAULT 'user';
-- Possible values: 'user', 'superadmin', 'support', 'readonly'
```

## Code Architecture

### Base Controller Pattern

```ruby
class SuperAdmin::BaseController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_admin!
  before_action :set_current_user
  before_action :log_admin_access

  layout 'admin'

  private

  def ensure_admin!
    redirect_to root_path unless current_user&.admin?
  end

  def set_current_user
    Current.user = current_user
  end

  def log_admin_access
    AdminAuditLog.log_action(
      user: Current.user,
      action: 'access',
      resource_type: controller_name,
      ip_address: request.remote_ip,
      user_agent: request.user_agent,
    )
  end
end
```

### Audit Logging Integration

```ruby
class SuperAdmin::AdminUsersController < SuperAdmin::BaseController
  def update
    @user = User.find(params[:id])

    if @user.update(user_params)
      AdminAuditLog.log_action(
        user: Current.user,
        action: 'update',
        resource: @user,
        change_data: @user.previous_changes,
        ip_address: request.remote_ip,
        user_agent: request.user_agent,
      )

      redirect_to super_admin_admin_user_path(@user),
                  notice: 'User updated successfully.'
    else
      render :edit
    end
  end

  private

  def user_params
    params.require(:user).permit(:email, :name, :verified, :role)
  end
end
```

### AdminAuditLog Model

```ruby
class AdminAuditLog < ApplicationRecord
  belongs_to :user
  belongs_to :resource, polymorphic: true, optional: true

  validates :action, presence: true
  validates :user_id, presence: true

  scope :recent, -> { order(created_at: :desc) }
  scope :by_user, ->(user) { where(user: user) }
  scope :by_action, ->(action) { where(action: action) }
  scope :by_resource_type, ->(type) { where(resource_type: type) }
  scope :with_changes, -> { where.not(change_data: nil) }

  def self.log_action(user:, action:, resource: nil, change_data: nil, ip_address: nil, user_agent: nil)
    create!(
      user: user,
      action: action.to_s,
      resource: resource,
      change_data: change_data,
      ip_address: ip_address,
      user_agent: user_agent
    )
  end

  def formatted_changes
    return {} unless change_data.present?

    change_data.transform_values do |change|
      case change
      when Array
        "#{change[0]} → #{change[1]}"
      else
        change.to_s
      end
    end
  end

  def resource_identifier
    return "#{resource_type} ##{resource_id}" unless resource

    case resource
    when User
      "#{resource.name} (#{resource.email})"
    when Job
      "#{resource.title}"
    when Organization
      "#{resource.name}"
    else
      "#{resource_type} ##{resource_id}"
    end
  end
end
```

## Frontend Architecture

### Layout Structure

```erb
<!-- app/views/layouts/admin.html.erb -->
<!DOCTYPE html>
<html>
  <head>
    <title>Admin - Ghostwrote</title>
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-stone-50">
    <div class="min-h-screen">
      <!-- Admin Navigation -->
      <%= render 'shared/admin_navigation' %>

      <!-- Main Content -->
      <main class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <%= render 'shared/flash_messages' %>
          <%= yield %>
        </div>
      </main>
    </div>
  </body>
</html>
```

### Component Patterns

```erb
<!-- Consistent table structure across all admin pages -->
<div class="bg-white rounded-lg shadow">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-stone-200">
    <h2 class="text-lg font-medium text-stone-900"><%= title %></h2>
  </div>

  <!-- Filters (if applicable) -->
  <% if filters_present? %>
    <div class="px-6 py-4 bg-stone-50 border-b border-stone-200">
      <%= render 'filters' %>
    </div>
  <% end %>

  <!-- Table -->
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-stone-200">
      <!-- Table content -->
    </table>
  </div>

  <!-- Footer with pagination -->
  <div class="px-6 py-4 border-t border-stone-200 bg-stone-50">
    <%= render 'pagination' if paginated? %>
  </div>
</div>
```

## Security Implementation

### Role-Based Access Control

```ruby
class User < ApplicationRecord
  enum role: {
         user: 'user',
         superadmin: 'superadmin',
         support: 'support',
         readonly: 'readonly',
       }

  def admin?
    %w[superadmin support readonly].include?(role)
  end

  def can_manage_users?
    superadmin?
  end

  def can_edit_content?
    %w[superadmin support].include?(role)
  end

  def can_delete_records?
    superadmin?
  end
end
```

### Permission Helpers

```ruby
module AdminPermissionHelper
  def can_edit?(resource)
    case Current.user.role
    when 'superadmin'
      true
    when 'support'
      !sensitive_resource?(resource)
    when 'readonly'
      false
    else
      false
    end
  end

  def can_delete?(resource)
    Current.user.superadmin?
  end

  private

  def sensitive_resource?(resource)
    resource.is_a?(User) && resource.admin?
  end
end
```

## Performance Considerations

### Database Optimization

```ruby
# Efficient queries with proper includes
class SuperAdmin::AdminUsersController < SuperAdmin::BaseController
  def index
    @users =
      User.includes(:organization).page(params[:page]).per(25).order(:name)

    # Apply filters efficiently
    @users = @users.where('name ILIKE ?', "%#{params[:search]}%") if params[
      :search
    ].present?
    @users = @users.where(role: params[:role]) if params[:role].present?
  end
end
```

### Caching Strategy

```ruby
# Cache expensive dashboard calculations
def dashboard_stats
  Rails
    .cache
    .fetch('admin_dashboard_stats', expires_in: 5.minutes) do
      {
        total_users: User.count,
        verified_users: User.where(verified: true).count,
        active_jobs: Job.published.count,
        pending_chat_requests: ChatRequest.pending.count,
        recent_signups: User.where('created_at > ?', 7.days.ago).count,
      }
    end
end
```

### Pagination & Search

```ruby
# Efficient search with pagination
def filtered_users
  users = User.all

  # Text search
  if params[:search].present?
    users =
      users.where(
        'name ILIKE :search OR email ILIKE :search',
        search: "%#{params[:search]}%",
      )
  end

  # Role filter
  users = users.where(role: params[:role]) if params[:role].present?

  # Date range filter
  if params[:start_date].present? && params[:end_date].present?
    users = users.where(created_at: params[:start_date]..params[:end_date])
  end

  users.page(params[:page]).per(25)
end
```

## Testing Strategy

### Model Tests

```ruby
# test/models/admin_audit_log_test.rb
class AdminAuditLogTest < ActiveSupport::TestCase
  test 'logs admin actions with all required fields' do
    user = users(:admin)
    target_user = users(:one)

    log =
      AdminAuditLog.log_action(
        user: user,
        action: 'update',
        resource: target_user,
        change_data: {
          'name' => ['Old Name', 'New Name'],
        },
        ip_address: '127.0.0.1',
        user_agent: 'Test Browser',
      )

    assert log.persisted?
    assert_equal user, log.user
    assert_equal 'update', log.action
    assert_equal target_user, log.resource
    assert_equal({ 'name' => ['Old Name', 'New Name'] }, log.change_data)
  end
end
```

### Controller Tests

```ruby
# test/controllers/super_admin/admin_users_controller_test.rb
class SuperAdmin::AdminUsersControllerTest < ActionDispatch::IntegrationTest
  setup do
    @admin = users(:admin)
    sign_in @admin
  end

  test 'should get index' do
    get super_admin_admin_users_path
    assert_response :success
    assert_select 'h1', 'Users'
  end

  test 'should update user and log action' do
    user = users(:regular_user)

    patch super_admin_admin_user_path(user),
          params: {
            user: {
              name: 'Updated Name',
            },
          }

    assert_redirected_to super_admin_admin_user_path(user)
    assert_equal 'Updated Name', user.reload.name

    # Verify audit log was created
    audit_log = AdminAuditLog.last
    assert_equal @admin, audit_log.user
    assert_equal 'update', audit_log.action
    assert_equal user, audit_log.resource
  end
end
```

## Deployment Considerations

### Environment Configuration

```ruby
# config/environments/production.rb
config.force_ssl = true # Ensure HTTPS for admin interface
config.log_level = :info # Appropriate logging level
config.cache_store = :redis_cache_store # For dashboard caching
```

### Database Migrations

```ruby
# Ensure proper indexes for admin queries
class AddAdminIndexes < ActiveRecord::Migration[7.0]
  def change
    add_index :users, :role
    add_index :users, :verified
    add_index :jobs, %i[status created_at]
    add_index :admin_audit_logs, %i[user_id created_at]
  end
end
```

### Monitoring & Alerts

```ruby
# Set up monitoring for admin interface health
class AdminHealthMonitor
  def self.check_critical_functions
    {
      admin_access: test_admin_access,
      audit_logging: test_audit_logging,
      database_performance: test_query_performance,
    }
  end
end
```

---

**Last Updated**: 2025-06-29  
**Version**: 1.0  
**Maintainer**: Development Team
